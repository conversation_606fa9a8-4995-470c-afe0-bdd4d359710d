#!/usr/bin/env python3
"""
EXTRACTEUR DE MÉTHODES - PROBABILITÉS CONDITIONNELLES LUPASCO
============================================================

Extracteur automatique pour identifier et extraire toutes les méthodes
contenant les équations de probabilités conditionnelles :

1. P(INDEX2(t+1)|INDEX1(t+1))
2. P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1))

Date: 2025-06-18
"""

import re
import os

def extraire_methode_complete(fichier_source, nom_methode, ligne_debut):
    """
    Extrait une méthode complète depuis le fichier source
    """
    with open(fichier_source, 'r', encoding='utf-8') as f:
        lignes = f.readlines()
    
    # Trouver la ligne de début de la méthode
    debut_trouve = False
    indentation_methode = None
    methode_lignes = []
    
    for i, ligne in enumerate(lignes):
        if i >= ligne_debut - 1:  # Convertir en index 0-based
            if not debut_trouve and f"def {nom_methode}" in ligne:
                debut_trouve = True
                indentation_methode = len(ligne) - len(ligne.lstrip())
                methode_lignes.append(ligne)
            elif debut_trouve:
                # Vérifier si on est encore dans la méthode
                if ligne.strip() == "":
                    # Ligne vide, continuer
                    methode_lignes.append(ligne)
                elif len(ligne) - len(ligne.lstrip()) > indentation_methode:
                    # Ligne indentée, fait partie de la méthode
                    methode_lignes.append(ligne)
                elif ligne.strip().startswith('#'):
                    # Commentaire au même niveau, peut faire partie de la méthode
                    methode_lignes.append(ligne)
                else:
                    # Nouvelle méthode ou fin de classe, arrêter
                    break
    
    return ''.join(methode_lignes)

def identifier_methodes_probabilites(fichier_source):
    """
    Identifie toutes les méthodes contenant les probabilités conditionnelles
    """
    methodes_identifiees = []
    
    # Patterns à rechercher
    patterns = [
        r'P\(INDEX2.*\|.*INDEX1.*\)',
        r'P\(INDEX3.*\|.*INDEX1.*INDEX2.*\)',
        r'def.*index2.*given.*index1',
        r'def.*index3.*given.*index1.*index2',
        r'def.*predire.*index[23]',
        r'def.*entrainer.*index[23]',
        r'probabilites_conditionnelles',
        r'probas_conditionnelles'
    ]
    
    with open(fichier_source, 'r', encoding='utf-8') as f:
        lignes = f.readlines()
    
    for i, ligne in enumerate(lignes):
        ligne_num = i + 1
        
        # Vérifier si c'est une définition de méthode
        if ligne.strip().startswith('def '):
            nom_methode = re.search(r'def\s+(\w+)', ligne)
            if nom_methode:
                nom_methode = nom_methode.group(1)
                
                # Lire les 20 lignes suivantes pour chercher les patterns
                contexte = ''.join(lignes[i:i+20])
                
                for pattern in patterns:
                    if re.search(pattern, contexte, re.IGNORECASE):
                        methodes_identifiees.append({
                            'nom': nom_methode,
                            'ligne': ligne_num,
                            'pattern_trouve': pattern,
                            'definition': ligne.strip()
                        })
                        break
    
    return methodes_identifiees

def extraire_toutes_methodes():
    """
    Fonction principale d'extraction
    """
    fichier_source = '../analyseur_sequences_lupasco.py'
    
    if not os.path.exists(fichier_source):
        print(f"❌ Fichier source non trouvé : {fichier_source}")
        return
    
    print("🔍 Identification des méthodes contenant les probabilités conditionnelles...")
    methodes = identifier_methodes_probabilites(fichier_source)
    
    print(f"✅ {len(methodes)} méthodes identifiées")
    
    # Créer le fichier d'extraction
    with open('methodes_probabilites_extraites.txt', 'w', encoding='utf-8') as f:
        f.write("MÉTHODES EXTRAITES - PROBABILITÉS CONDITIONNELLES LUPASCO\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Date d'extraction : 2025-06-18\n")
        f.write(f"Source : {fichier_source}\n")
        f.write(f"Nombre de méthodes : {len(methodes)}\n\n")
        
        for i, methode in enumerate(methodes, 1):
            f.write(f"\n{'='*80}\n")
            f.write(f"MÉTHODE {i}/{len(methodes)} : {methode['nom']}\n")
            f.write(f"{'='*80}\n")
            f.write(f"Ligne : {methode['ligne']}\n")
            f.write(f"Pattern trouvé : {methode['pattern_trouve']}\n")
            f.write(f"Définition : {methode['definition']}\n")
            f.write(f"\n{'-'*60}\n")
            f.write("CODE COMPLET :\n")
            f.write(f"{'-'*60}\n\n")
            
            # Extraire le code complet de la méthode
            code_complet = extraire_methode_complete(fichier_source, methode['nom'], methode['ligne'])
            f.write(code_complet)
            f.write(f"\n{'-'*60}\n")
            f.write("FIN DE MÉTHODE\n")
            f.write(f"{'-'*60}\n\n")
    
    print("✅ Extraction terminée : methodes_probabilites_extraites.txt")
    
    # Résumé
    print(f"\n📋 RÉSUMÉ DES MÉTHODES EXTRAITES :")
    for i, methode in enumerate(methodes, 1):
        print(f"   {i}. {methode['nom']} (ligne {methode['ligne']})")

if __name__ == "__main__":
    extraire_toutes_methodes()
