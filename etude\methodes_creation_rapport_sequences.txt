MÉTHODES IMPLIQUÉES DANS LA CRÉATION DU RAPPORT_ANALYSE_SEQUENCES
================================================================

Date: 2025-06-18
Fichier analysé: rapport_analyse_sequences_20250618_025805.txt
Source: analyseur_sequences_lupasco.py

## MÉTHODE PRINCIPALE DE GÉNÉRATION

### generer_rapport() - Ligne 1996
**Rôle:** Méthode principale qui génère le rapport complet
**Contenu généré:**
- En-tête du rapport avec date, fichier source, nombre de parties
- Sections pour INDEX1, INDEX2, INDEX3
- Section probabilités conditionnelles
- Section prédictions Lupasco

## MÉTHODES D'ANALYSE DIRECTEMENT IMPLIQUÉES

### 1. ANALYSE INDEX1 (SYNC/DESYNC)

#### analyser_runs() - Ligne 295
**Contenu généré dans le rapport:**
- <PERSON><PERSON> de la séquence : 6,630,026 éléments
- Entropie de Shannon : 1.0000 bits
- Analyse des runs par valeur (SYNC/DESYNC)
- Nombre de runs, longueur moyenne, médiane, max, écart-type
- Probabilité, longueur moyenne théorique
- Distribution complète des longueurs (lignes 26-64 et 75-114)
- Analyse globale (lignes 116-121)
- Runs test (lignes 122-127)
- Autocorrélation (lignes 129-140)

#### _calculer_entropie_shannon() - Ligne 471
**Contenu généré:**
- Entropie de Shannon : 1.0000 bits (ligne 12)

#### _analyser_runs_par_valeur() - Ligne 350
**Contenu généré:**
- Statistiques descriptives pour SYNC et DESYNC
- Nombre de runs, longueurs moyennes, médianes, max
- Écarts-types, probabilités
- Distributions complètes des longueurs

#### _calculer_runs_test() - Ligne 430
**Contenu généré:**
- Runs observés : 2029334
- Runs attendus : 3314903.14
- Z-score : -998.5789
- P-value : 0.0000
- Significatif : Oui

#### _calculer_autocorrelation() - Ligne 450
**Contenu généré:**
- Autocorrélation Lag 1 à 10 (lignes 129-140)

### 2. ANALYSE INDEX2 (pair_4/pair_6/impair_5)

#### analyser_runs() - Ligne 295
**Contenu généré dans le rapport:**
- Taille de la séquence : 6,630,026 éléments
- Entropie de Shannon : 1.5781 bits
- Analyse des runs par valeur (impair_5, pair_6, pair_4)
- Statistiques pour chaque valeur (lignes 150-221)
- Analyse globale (lignes 222-226)
- Autocorrélation (lignes 228-239)

#### _calculer_entropie_shannon() - Ligne 471
**Contenu généré:**
- Entropie de Shannon : 1.5781 bits (ligne 144)

#### _analyser_runs_par_valeur() - Ligne 350
**Contenu généré:**
- Statistiques pour impair_5, pair_6, pair_4
- Distributions complètes des longueurs pour chaque valeur

#### _calculer_autocorrelation() - Ligne 450
**Contenu généré:**
- Autocorrélation Lag 1 à 10 pour INDEX2 (lignes 228-239)

### 3. ANALYSE INDEX3 (PLAYER/BANKER)

#### analyser_runs() - Ligne 295
**Contenu généré dans le rapport:**
- Taille de la séquence : 5,999,998 éléments
- Entropie de Shannon : 0.9999 bits
- Analyse des runs par valeur (PLAYER, BANKER)
- Statistiques pour chaque valeur (lignes 249-311)
- Analyse globale (lignes 313-317)
- Runs test (lignes 319-324)
- Autocorrélation (lignes 326-337)

#### _calculer_entropie_shannon() - Ligne 471
**Contenu généré:**
- Entropie de Shannon : 0.9999 bits (ligne 244)

#### _analyser_runs_par_valeur() - Ligne 350
**Contenu généré:**
- Statistiques pour PLAYER et BANKER
- Distributions complètes des longueurs

#### _calculer_runs_test() - Ligne 430
**Contenu généré:**
- Runs observés : 2999571
- Runs attendus : 2999499.71
- Z-score : 0.0582
- P-value : 0.9536
- Significatif : Non

#### _calculer_autocorrelation() - Ligne 450
**Contenu généré:**
- Autocorrélation Lag 1 à 10 pour INDEX3 (lignes 326-337)

### 4. SECTION PREDICTIONS_LUPASCO

#### analyser_predictions_lupasco() - Ligne 556
**Contenu généré:**
- Section "ANALYSE predictions_lupasco" (ligne 339)
- Contenu vide dans ce rapport spécifique

## MÉTHODES DE SUPPORT ET CALCULS

### _extraire_sequences() - Ligne 216
**Rôle:** Extraction des séquences INDEX1, INDEX2, INDEX3 depuis le JSON
**Impact:** Fournit les données de base pour toutes les analyses

### _charger_avec_streaming() - Ligne 108
**Rôle:** Chargement efficace du fichier JSON volumineux
**Impact:** Permet l'accès aux données pour l'analyse

### _calculer_statistiques_descriptives() - Ligne 380
**Rôle:** Calcul des statistiques de base (moyenne, médiane, écart-type, etc.)
**Impact:** Génère les statistiques affichées pour chaque valeur

### _calculer_longueur_moyenne_theorique() - Ligne 410
**Rôle:** Calcul théorique basé sur les probabilités
**Impact:** Fournit les valeurs de comparaison théorique

### _calculer_ecarts() - Ligne 420
**Rôle:** Calcul des écarts entre observé et théorique
**Impact:** Génère les écarts affichés dans le rapport

## FLUX DE GÉNÉRATION DU RAPPORT

### 1. INITIALISATION
```
generer_rapport() → Création fichier → En-tête
```

### 2. POUR CHAQUE INDEX (INDEX1, INDEX2, INDEX3)
```
Boucle sur self.resultats → 
  Écriture section INDEX →
  Extraction données runs →
  Écriture statistiques par valeur →
  Écriture analyse globale →
  Écriture autocorrélation
```

### 3. SECTIONS SPÉCIALISÉES
```
Probabilités conditionnelles (si disponibles) →
Prédictions Lupasco (si disponibles)
```

## STRUCTURE DES DONNÉES UTILISÉES

### self.resultats[nom_index]
```
{
  'taille_sequence': int,
  'entropie_shannon': float,
  'runs': {
    'valeur1': {
      'statistiques': {...},
      'theorique': {...},
      'ecarts': {...},
      'tests': {...}
    },
    'global': {
      'nombre_total_runs': int,
      'longueur_moyenne_globale': float,
      'runs_test': {...}
    }
  },
  'autocorrelation': {lag: correlation}
}
```

## MÉTHODES NON UTILISÉES DANS CE RAPPORT

Les méthodes suivantes existent mais ne contribuent PAS au rapport analysé :
- Toutes les méthodes de probabilités conditionnelles (section vide)
- Toutes les méthodes de prédictions Lupasco (section vide)
- Méthodes de validation et tests statistiques avancés

## CONCLUSION

Le rapport rapport_analyse_sequences_20250618_025805.txt est généré principalement par :
1. **generer_rapport()** (méthode principale)
2. **analyser_runs()** (analyse des runs pour chaque INDEX)
3. **Méthodes de calcul statistique** (entropie, autocorrélation, runs test)
4. **Méthodes de support** (extraction, chargement, calculs)

**Total : 8 méthodes principales directement impliquées dans le contenu du rapport.**
