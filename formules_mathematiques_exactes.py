# -*- coding: utf-8 -*-
"""
FORMULES MATHÉMATIQUES EXACTES POUR L'ANALYSE STATISTIQUE
=========================================================

Ce fichier contient les formules mathématiques exactes vérifiées à partir de sources 
académiques fiables (Wikipedia, sources universitaires) pour l'analyse statistique 
des données de baccarat et du système Lupasco.

Toutes les formules sont implémentées avec les bibliothèques Python validées :
- numpy : calculs numériques
- scipy.stats : tests statistiques
- pandas : manipulation de données
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import entropy
import warnings

# ============================================================================
# 1. COEFFICIENT DE GINI
# ============================================================================

def gini_coefficient(x):
    """
    Calcule le coefficient de Gini exact.
    
    Formule exacte : G = (2 * Σ(i * x_i)) / (n * Σ(x_i)) - (n + 1) / n
    où x_i sont les valeurs triées par ordre croissant.
    
    Source : Wikipedia - Gini coefficient
    
    Args:
        x (array-like): Données à analyser
        
    Returns:
        float: Coefficient de Gini (0 = égalité parfaite, 1 = inégalité maximale)
    """
    x = np.array(x, dtype=float)
    x = x[~np.isnan(x)]  # Supprimer les NaN
    
    if len(x) == 0:
        return np.nan
    
    # Trier les valeurs par ordre croissant
    x_sorted = np.sort(x)
    n = len(x_sorted)
    
    # Formule exacte du coefficient de Gini
    numerator = 2 * np.sum((np.arange(1, n + 1) * x_sorted))
    denominator = n * np.sum(x_sorted)
    
    if denominator == 0:
        return 0.0
    
    gini = (numerator / denominator) - (n + 1) / n
    return gini


# ============================================================================
# 2. ENTROPIE DE SHANNON
# ============================================================================

def shannon_entropy(probabilities):
    """
    Calcule l'entropie de Shannon exacte.
    
    Formule exacte : H(X) = -Σ(p_i * log₂(p_i))
    où p_i sont les probabilités des événements.
    
    Source : Shannon, C.E. (1948). "A Mathematical Theory of Communication"
    
    Args:
        probabilities (array-like): Probabilités des événements
        
    Returns:
        float: Entropie de Shannon en bits
    """
    p = np.array(probabilities, dtype=float)
    p = p[p > 0]  # Supprimer les probabilités nulles
    
    if len(p) == 0:
        return 0.0
    
    # Formule exacte de l'entropie de Shannon
    return -np.sum(p * np.log2(p))


def shannon_entropy_from_data(data):
    """
    Calcule l'entropie de Shannon à partir de données brutes.
    
    Args:
        data (array-like): Données brutes
        
    Returns:
        float: Entropie de Shannon en bits
    """
    unique, counts = np.unique(data, return_counts=True)
    probabilities = counts / len(data)
    return shannon_entropy(probabilities)


# ============================================================================
# 3. FONCTION D'AUTOCORRÉLATION
# ============================================================================

def autocorrelation_function(x, max_lag=None):
    """
    Calcule la fonction d'autocorrélation exacte.
    
    Formule exacte pour processus stationnaire au sens large :
    ρ_XX(τ) = E[(X_{t+τ} - μ)(X_t - μ)] / σ²
    
    Source : Wikipedia - Autocorrelation
    
    Args:
        x (array-like): Série temporelle
        max_lag (int): Nombre maximum de décalages à calculer
        
    Returns:
        numpy.ndarray: Fonction d'autocorrélation
    """
    x = np.array(x, dtype=float)
    n = len(x)
    
    if max_lag is None:
        max_lag = min(n // 4, 40)  # Limite raisonnable
    
    # Centrer la série
    x_centered = x - np.mean(x)
    variance = np.var(x, ddof=1)
    
    if variance == 0:
        return np.ones(max_lag + 1)
    
    autocorr = np.zeros(max_lag + 1)
    
    for lag in range(max_lag + 1):
        if lag == 0:
            autocorr[lag] = 1.0
        else:
            if n - lag > 0:
                covariance = np.mean(x_centered[:-lag] * x_centered[lag:])
                autocorr[lag] = covariance / variance
            else:
                autocorr[lag] = 0.0
    
    return autocorr


# ============================================================================
# 4. COEFFICIENT DE VARIATION
# ============================================================================

def coefficient_of_variation(x):
    """
    Calcule le coefficient de variation exact.
    
    Formule exacte : CV = σ / μ
    où σ est l'écart-type et μ la moyenne.
    
    Source : Wikipedia - Coefficient of variation
    
    Args:
        x (array-like): Données à analyser
        
    Returns:
        float: Coefficient de variation
    """
    x = np.array(x, dtype=float)
    x = x[~np.isnan(x)]
    
    if len(x) == 0:
        return np.nan
    
    mean_val = np.mean(x)
    
    if mean_val == 0:
        return np.inf if np.std(x, ddof=1) > 0 else np.nan
    
    std_val = np.std(x, ddof=1)
    return std_val / abs(mean_val)


# ============================================================================
# 5. TEST DES RUNS (WALD-WOLFOWITZ)
# ============================================================================

def runs_test(sequence):
    """
    Effectue le test des runs de Wald-Wolfowitz pour la randomness.
    
    Formule exacte pour la statistique Z :
    Z = (R - E[R]) / √Var[R]
    où R est le nombre de runs observés.
    
    Source : Wald, A. and Wolfowitz, J. (1940)
    
    Args:
        sequence (array-like): Séquence binaire ou convertible en binaire
        
    Returns:
        dict: Résultats du test (statistique, p-value, etc.)
    """
    sequence = np.array(sequence)
    
    # Convertir en séquence binaire si nécessaire
    if len(np.unique(sequence)) != 2:
        median_val = np.median(sequence)
        binary_seq = (sequence > median_val).astype(int)
    else:
        binary_seq = sequence
    
    # Compter les runs
    runs = 1
    for i in range(1, len(binary_seq)):
        if binary_seq[i] != binary_seq[i-1]:
            runs += 1
    
    # Compter les éléments de chaque type
    n1 = np.sum(binary_seq == 0)
    n2 = np.sum(binary_seq == 1)
    n = n1 + n2
    
    if n1 == 0 or n2 == 0:
        return {
            'statistic': np.nan,
            'pvalue': np.nan,
            'runs_observed': runs,
            'runs_expected': np.nan
        }
    
    # Formules exactes pour l'espérance et la variance
    expected_runs = (2 * n1 * n2) / n + 1
    variance_runs = (2 * n1 * n2 * (2 * n1 * n2 - n)) / (n**2 * (n - 1))
    
    if variance_runs <= 0:
        return {
            'statistic': np.nan,
            'pvalue': np.nan,
            'runs_observed': runs,
            'runs_expected': expected_runs
        }
    
    # Statistique Z avec correction de continuité
    if runs > expected_runs:
        z_stat = (runs - 0.5 - expected_runs) / np.sqrt(variance_runs)
    else:
        z_stat = (runs + 0.5 - expected_runs) / np.sqrt(variance_runs)
    
    # P-value bilatérale
    p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))
    
    return {
        'statistic': z_stat,
        'pvalue': p_value,
        'runs_observed': runs,
        'runs_expected': expected_runs,
        'n1': n1,
        'n2': n2
    }


# ============================================================================
# 6. FONCTIONS UTILITAIRES POUR L'ANALYSE LUPASCO
# ============================================================================

def lupasco_entropy_analysis(index1_data, index2_data, index3_data):
    """
    Analyse complète de l'entropie pour le système Lupasco.
    
    Args:
        index1_data: Données INDEX1 (SYNC/DESYNC)
        index2_data: Données INDEX2 (pair_4/pair_6/impair_5)
        index3_data: Données INDEX3 (PLAYER/BANKER/TIE)
        
    Returns:
        dict: Résultats de l'analyse entropique
    """
    results = {}
    
    # Entropie de chaque index
    results['entropy_index1'] = shannon_entropy_from_data(index1_data)
    results['entropy_index2'] = shannon_entropy_from_data(index2_data)
    results['entropy_index3'] = shannon_entropy_from_data(index3_data)
    
    # Entropie jointe INDEX1-INDEX2
    joint_12 = list(zip(index1_data, index2_data))
    results['entropy_joint_12'] = shannon_entropy_from_data(joint_12)
    
    # Information mutuelle INDEX1-INDEX2
    results['mutual_info_12'] = (results['entropy_index1'] + 
                                results['entropy_index2'] - 
                                results['entropy_joint_12'])
    
    return results


def lupasco_statistical_summary(data_dict):
    """
    Résumé statistique complet pour les données Lupasco.
    
    Args:
        data_dict (dict): Dictionnaire contenant les séries de données
        
    Returns:
        pandas.DataFrame: Résumé statistique
    """
    summary_data = []
    
    for name, data in data_dict.items():
        data_array = np.array(data)
        
        summary = {
            'Variable': name,
            'Count': len(data_array),
            'Mean': np.mean(data_array) if data_array.dtype.kind in 'biufc' else np.nan,
            'Std': np.std(data_array, ddof=1) if data_array.dtype.kind in 'biufc' else np.nan,
            'CV': coefficient_of_variation(data_array) if data_array.dtype.kind in 'biufc' else np.nan,
            'Gini': gini_coefficient(data_array) if data_array.dtype.kind in 'biufc' else np.nan,
            'Entropy': shannon_entropy_from_data(data_array),
            'Unique_Values': len(np.unique(data_array))
        }
        
        summary_data.append(summary)
    
    return pd.DataFrame(summary_data)


# ============================================================================
# 7. VALIDATION DES FORMULES
# ============================================================================

def validate_formulas():
    """
    Valide les formules implémentées avec des cas de test connus.
    
    Returns:
        dict: Résultats de validation
    """
    validation_results = {}
    
    # Test 1: Coefficient de Gini pour distribution uniforme
    uniform_data = np.ones(100)
    gini_uniform = gini_coefficient(uniform_data)
    validation_results['gini_uniform'] = abs(gini_uniform) < 1e-10
    
    # Test 2: Entropie de Shannon pour distribution uniforme
    uniform_probs = np.ones(4) / 4
    entropy_uniform = shannon_entropy(uniform_probs)
    expected_entropy = 2.0  # log₂(4) = 2
    validation_results['entropy_uniform'] = abs(entropy_uniform - expected_entropy) < 1e-10
    
    # Test 3: Autocorrélation à lag 0
    test_data = np.random.randn(100)
    autocorr = autocorrelation_function(test_data, max_lag=1)
    validation_results['autocorr_lag0'] = abs(autocorr[0] - 1.0) < 1e-10
    
    # Test 4: Coefficient de variation pour données constantes
    constant_data = np.ones(50) * 5
    cv_constant = coefficient_of_variation(constant_data)
    validation_results['cv_constant'] = abs(cv_constant) < 1e-10
    
    return validation_results


if __name__ == "__main__":
    # Validation des formules
    print("Validation des formules mathématiques exactes...")
    results = validate_formulas()
    
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    print("\nToutes les formules sont validées et prêtes à l'utilisation.")
