#!/usr/bin/env python3
"""
TEST DE LA NOUVELLE FONCTIONNALITÉ INDEX2_INDEX3
=================================================

Script de test pour vérifier que l'analyse INDEX2_INDEX3 avec formules exactes
fonctionne correctement dans l'analyseur.py modifié.

Date: 2025-06-18
"""

import sys
import os
from analyseur import AnalyseurSequencesLupasco

def test_index2_index3_analysis():
    """
    Teste la nouvelle fonctionnalité d'analyse INDEX2_INDEX3
    """
    print("🧪 TEST DE L'ANALYSE INDEX2_INDEX3 AVEC FORMULES EXACTES")
    print("=" * 60)
    
    # Vérifier que le fichier JSON existe
    fichier_json = "dataset_baccarat_lupasco_20250617_232800.json"
    
    if not os.path.exists(fichier_json):
        print(f"❌ Fichier {fichier_json} non trouvé")
        print("💡 Assurez-vous que le fichier JSON est dans le répertoire courant")
        return False
    
    try:
        print(f"📂 Fichier trouvé : {fichier_json}")
        
        # Créer l'analyseur
        print("\n🔧 Création de l'analyseur...")
        analyseur = AnalyseurSequencesLupasco(fichier_json)
        
        # Charger les données (mode streaming pour gros fichiers)
        print("\n📊 Chargement des données...")
        analyseur.charger_donnees()
        
        # Vérifier que INDEX2_INDEX3 est bien présent
        if 'INDEX2_INDEX3' not in analyseur.sequences:
            print("❌ Séquence INDEX2_INDEX3 non trouvée")
            return False

        sequence_index2_index3 = analyseur.sequences['INDEX2_INDEX3']
        print(f"✅ Séquence INDEX2_INDEX3 chargée : {len(sequence_index2_index3):,} éléments")

        # Afficher un échantillon des combinaisons
        combinaisons_uniques = sorted(set(sequence_index2_index3))
        print(f"📋 Combinaisons INDEX2_INDEX3 trouvées : {len(combinaisons_uniques)}")

        for combo in combinaisons_uniques:
            count = sequence_index2_index3.count(combo)
            freq = count / len(sequence_index2_index3)
            print(f"   - {combo} : {count:,} fois ({freq:.4f})")

        # Tester la nouvelle méthode d'analyse
        print("\n🔬 Test de l'analyse INDEX2_INDEX3 avec formules exactes...")

        try:
            resultats = analyseur.analyser_index2_index3_avec_formules_exactes()
            print("✅ Analyse INDEX2_INDEX3 réussie !")
            
            # Vérifier les résultats
            if 'analyse_globale' in resultats:
                globale = resultats['analyse_globale']
                print(f"   📊 Entropie Shannon : {globale['entropie_shannon']:.6f} bits")
                print(f"   📊 Coefficient Gini : {globale['coefficient_gini']:.6f}")
                print(f"   📊 Autocorrélation lag 1 : {globale['autocorrelation'][1]:.6f}")
                print(f"   📊 Test runs p-value : {globale['runs_test']['pvalue']:.6f}")
            
            if 'analyse_par_combinaison' in resultats:
                print(f"   📋 Analyse par combinaison : {len(resultats['analyse_par_combinaison'])} combinaisons")
            
            if 'transitions' in resultats:
                print("   🔄 Analyse des transitions : OK")
            
            if 'patterns_temporels' in resultats:
                print("   ⏰ Analyse des patterns temporels : OK")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse INDEX2_INDEX3 : {e}")
            return False
    
    except Exception as e:
        print(f"❌ Erreur générale : {e}")
        return False

def test_rapport_complet():
    """
    Teste la génération du rapport complet incluant INDEX1_INDEX2
    """
    print("\n📝 TEST DE GÉNÉRATION DU RAPPORT COMPLET")
    print("-" * 45)
    
    fichier_json = "dataset_baccarat_lupasco_20250617_232800.json"
    
    if not os.path.exists(fichier_json):
        print(f"❌ Fichier {fichier_json} non trouvé")
        return False
    
    try:
        # Créer l'analyseur
        analyseur = AnalyseurSequencesLupasco(fichier_json)
        
        # Charger les données
        analyseur.charger_donnees()
        
        # Lancer l'analyse complète (incluant INDEX1_INDEX2)
        print("🔄 Lancement de l'analyse complète...")
        analyseur.analyser_toutes_sequences()
        
        # Générer le rapport
        print("📄 Génération du rapport...")
        fichier_rapport = analyseur.generer_rapport("test_rapport_index1_index2.txt")
        
        # Vérifier que le rapport contient la section INDEX1_INDEX2
        if os.path.exists(fichier_rapport):
            with open(fichier_rapport, 'r', encoding='utf-8') as f:
                contenu = f.read()
                
            if "INDEX1_INDEX2 AVEC FORMULES MATHÉMATIQUES EXACTES" in contenu:
                print("✅ Section INDEX1_INDEX2 trouvée dans le rapport")
                
                # Compter les combinaisons dans le rapport
                lignes_combinaisons = [line for line in contenu.split('\n') 
                                     if 'Combinaison' in line and '/6 :' in line]
                print(f"   📋 Combinaisons INDEX1_INDEX2 dans le rapport : {len(lignes_combinaisons)}")
                
                return True
            else:
                print("❌ Section INDEX1_INDEX2 non trouvée dans le rapport")
                return False
        else:
            print(f"❌ Fichier rapport {fichier_rapport} non créé")
            return False
    
    except Exception as e:
        print(f"❌ Erreur lors du test du rapport : {e}")
        return False

def main():
    """
    Fonction principale de test
    """
    print("🚀 DÉMARRAGE DES TESTS INDEX1_INDEX2")
    print("=" * 50)
    
    # Test 1 : Analyse INDEX1_INDEX2
    test1_ok = test_index1_index2_analysis()
    
    # Test 2 : Rapport complet
    test2_ok = test_rapport_complet()
    
    # Résumé des tests
    print("\n📊 RÉSUMÉ DES TESTS")
    print("-" * 20)
    print(f"Test 1 - Analyse INDEX1_INDEX2 : {'✅ RÉUSSI' if test1_ok else '❌ ÉCHEC'}")
    print(f"Test 2 - Rapport complet : {'✅ RÉUSSI' if test2_ok else '❌ ÉCHEC'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ La nouvelle fonctionnalité INDEX1_INDEX2 est opérationnelle")
        return True
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
