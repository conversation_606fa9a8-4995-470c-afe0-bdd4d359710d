#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT RAPIDE DE CONVERSION JSON VERS TXT
==========================================

Script simplifié pour convertir rapidement un fichier JSON Lupasco en TXT.
Détecte automatiquement les fichiers JSON dans le répertoire courant.

Usage:
    python convertir_json_txt.py

Le script vous proposera les fichiers JSON disponibles et effectuera la conversion.
"""

import os
import glob
from convertisseur_json_vers_txt import convertir_json_vers_txt


def main():
    """Fonction principale avec interface interactive"""
    print("🔄 CONVERSION RAPIDE JSON → TXT")
    print("=" * 40)
    
    # Chercher les fichiers JSON dans le répertoire courant
    fichiers_json = glob.glob("*.json")
    
    if not fichiers_json:
        print("❌ Aucun fichier JSON trouvé dans le répertoire courant.")
        return
    
    print(f"📂 {len(fichiers_json)} fichier(s) JSON trouvé(s) :")
    for i, fichier in enumerate(fichiers_json, 1):
        taille = os.path.getsize(fichier)
        taille_mb = taille / (1024 * 1024)
        print(f"  {i}. {fichier} ({taille_mb:.1f} MB)")
    
    # Demander à l'utilisateur de choisir
    if len(fichiers_json) == 1:
        choix = 1
        print(f"\n🎯 Conversion automatique du seul fichier : {fichiers_json[0]}")
    else:
        try:
            choix = int(input(f"\n🎯 Choisissez un fichier (1-{len(fichiers_json)}) : "))
            if choix < 1 or choix > len(fichiers_json):
                print("❌ Choix invalide.")
                return
        except ValueError:
            print("❌ Veuillez entrer un nombre valide.")
            return
    
    fichier_choisi = fichiers_json[choix - 1]
    
    try:
        print(f"\n🔄 Conversion en cours...")
        fichier_genere = convertir_json_vers_txt(fichier_choisi)
        
        # Afficher les informations sur les fichiers
        taille_json = os.path.getsize(fichier_choisi) / (1024 * 1024)
        taille_txt = os.path.getsize(fichier_genere) / (1024 * 1024)
        
        print(f"\n✅ CONVERSION RÉUSSIE !")
        print(f"📂 JSON source : {fichier_choisi} ({taille_json:.1f} MB)")
        print(f"📝 TXT généré : {fichier_genere} ({taille_txt:.1f} MB)")
        print(f"📊 Ratio de compression : {taille_txt/taille_json:.1f}x")
        
    except Exception as e:
        print(f"\n❌ ERREUR : {e}")


if __name__ == "__main__":
    main()
