FORMULES STATISTIQUES POUR L'ANALYSE INDEX5 - LUPASCO
======================================================

Date: 2025-06-18
Source: Recherche universitaire et mathématique

OBJECTIF: Analyser les 18 combinaisons INDEX5 avec des formules statistiques
avancées pour mesurer dispersion, concentration, alternance et fréquence.

1. FORMULES UTILISANT L'ÉCART-TYPE
===================================

1.1 COEFFICIENT DE VARIATION (CV)
----------------------------------
Formule: CV = σ/μ × 100%
Où: σ = écart-type, μ = moyenne

Application INDEX5:
- Mesure la variabilité relative de chaque combinaison
- Permet de comparer les 18 combinaisons indépendamment de leur fréquence
- CV faible = comportement prévisible
- CV élevé = comportement variable

1.2 ÉCART-TYPE NORMALISÉ
------------------------
Formule: σ_norm = σ/√n
Où: n = taille de l'échantillon

Application INDEX5:
- Ajuste l'écart-type selon la taille des runs
- Permet comparaison équitable entre combinaisons rares et fréquentes

1.3 INTERVALLE DE CONFIANCE
---------------------------
Formule: IC = μ ± z_(α/2) × (σ/√n)
Où: z_(α/2) = valeur critique, α = niveau de signification

Application INDEX5:
- Détermine la plage probable des longueurs de runs
- Identifie les combinaisons avec comportement anormal

2. FORMULES DE DISPERSION ET CONCENTRATION
==========================================

2.1 COEFFICIENT DE GINI
------------------------
Formule: G = (1/2n²μ) × Σᵢ Σⱼ |xᵢ - xⱼ|
Où: n = nombre d'observations, μ = moyenne

Application INDEX5:
- Mesure l'inégalité dans la distribution des longueurs de runs
- G = 0 : égalité parfaite (tous runs même longueur)
- G = 1 : inégalité maximale (un seul run très long)

2.2 INDICE DE CONCENTRATION HERFINDAHL-HIRSCHMAN (HHI)
------------------------------------------------------
Formule: HHI = Σᵢ (pᵢ)²
Où: pᵢ = proportion de la catégorie i

Application INDEX5:
- Mesure la concentration des longueurs de runs
- HHI proche de 1 : concentration élevée (dominance d'une longueur)
- HHI proche de 0 : distribution uniforme

2.3 ENTROPIE DE SHANNON
-----------------------
Formule: H = -Σᵢ pᵢ × log₂(pᵢ)
Où: pᵢ = probabilité de la catégorie i

Application INDEX5:
- Mesure le désordre/imprévisibilité des longueurs de runs
- H élevée = distribution uniforme (imprévisible)
- H faible = concentration sur quelques longueurs (prévisible)

2.4 INDICE DE SIMPSON
---------------------
Formule: D = Σᵢ pᵢ²
Où: pᵢ = proportion relative de chaque catégorie

Application INDEX5:
- Mesure la probabilité que deux runs choisis au hasard aient même longueur
- Complément: 1-D = diversité de Simpson

2.5 VARIANCE RELATIVE
---------------------
Formule: VR = σ²/μ²
Où: σ² = variance, μ = moyenne

Application INDEX5:
- Mesure la dispersion relative indépendamment de l'échelle
- Permet comparaison entre combinaisons de fréquences différentes

3. FORMULES D'ALTERNANCE ET FRÉQUENCE
=====================================

3.1 TEST DES RUNS (WALD-WOLFOWITZ)
----------------------------------
Formule: Z = (R - μᵣ)/σᵣ
Où:
- R = nombre de runs observés
- μᵣ = (2n₁n₂)/(n₁+n₂) + 1 (moyenne théorique)
- σᵣ² = (2n₁n₂(2n₁n₂-n₁-n₂))/((n₁+n₂)²(n₁+n₂-1)) (variance théorique)
- n₁, n₂ = effectifs des deux catégories

Application INDEX5:
- Teste la randomness des séquences
- Z proche de 0 = séquence aléatoire
- Z négatif = trop peu de runs (clustering)
- Z positif = trop de runs (alternance excessive)

3.2 AUTOCORRÉLATION À LAG k
---------------------------
Formule: ρₖ = Σᵢ(xᵢ - μ)(xᵢ₊ₖ - μ) / Σᵢ(xᵢ - μ)²
Où: k = décalage temporel

Application INDEX5:
- Mesure la corrélation entre une combinaison et elle-même k positions plus tard
- ρₖ > 0 = persistance (tendance à se répéter)
- ρₖ < 0 = alternance (tendance à éviter la répétition)
- ρₖ ≈ 0 = indépendance

3.3 FRÉQUENCE RELATIVE CONDITIONNELLE
-------------------------------------
Formule: P(Aᵢ₊₁|Aᵢ) = N(Aᵢ→Aᵢ₊₁)/N(Aᵢ)
Où: N(Aᵢ→Aᵢ₊₁) = transitions de Aᵢ vers Aᵢ₊₁

Application INDEX5:
- Probabilité qu'une combinaison soit suivie d'elle-même
- Mesure directe de la persistance/anti-persistance

3.4 INDICE D'ALTERNANCE
-----------------------
Formule: IA = (Nₐₗₜ/Nₜₒₜₐₗ) × 100%
Où: Nₐₗₜ = nombre d'alternances, Nₜₒₜₐₗ = nombre total de transitions

Application INDEX5:
- Pourcentage de fois où une combinaison change
- IA élevé = forte alternance
- IA faible = forte persistance

4. FORMULES DE FORME DE DISTRIBUTION
====================================

4.1 COEFFICIENT D'ASYMÉTRIE (SKEWNESS)
--------------------------------------
Formule: γ₁ = E[(X-μ)³]/σ³
Où: E = espérance mathématique

Application INDEX5:
- γ₁ = 0 : distribution symétrique
- γ₁ > 0 : asymétrie positive (queue à droite)
- γ₁ < 0 : asymétrie négative (queue à gauche)

4.2 COEFFICIENT D'APLATISSEMENT (KURTOSIS)
------------------------------------------
Formule: γ₂ = E[(X-μ)⁴]/σ⁴ - 3
Où: -3 normalise par rapport à la distribution normale

Application INDEX5:
- γ₂ = 0 : distribution normale (mésokurtique)
- γ₂ > 0 : distribution pointue (leptokurtique)
- γ₂ < 0 : distribution aplatie (platykurtique)

5. FORMULES SPÉCIALISÉES POUR SÉQUENCES
=======================================

5.1 LONGUEUR MOYENNE THÉORIQUE DES RUNS
---------------------------------------
Formule: E[L] = 1/p
Où: p = probabilité d'occurrence de l'événement

Application INDEX5:
- Longueur attendue des runs pour chaque combinaison
- Comparaison avec longueur observée pour détecter anomalies

5.2 VARIANCE THÉORIQUE DES RUNS
-------------------------------
Formule: Var[L] = (1-p)/p²
Où: p = probabilité d'occurrence

Application INDEX5:
- Variabilité attendue des longueurs de runs
- Test de conformité à la distribution géométrique

5.3 COEFFICIENT DE CLUSTERING
-----------------------------
Formule: CC = (L_obs - L_théo)/L_théo
Où: L_obs = longueur moyenne observée, L_théo = longueur théorique

Application INDEX5:
- CC > 0 : tendance au clustering (runs plus longs qu'attendu)
- CC < 0 : tendance à l'alternance (runs plus courts qu'attendu)
- CC ≈ 0 : comportement aléatoire

6. TESTS STATISTIQUES COMPLÉMENTAIRES
=====================================

6.1 TEST DE KOLMOGOROV-SMIRNOV
------------------------------
Formule: D = max|F_n(x) - F₀(x)|
Où: F_n = fonction de répartition empirique, F₀ = fonction théorique

Application INDEX5:
- Teste si la distribution des longueurs suit une loi géométrique
- Valide les hypothèses de randomness

6.2 TEST CHI-CARRÉ D'AJUSTEMENT
-------------------------------
Formule: χ² = Σᵢ (Oᵢ - Eᵢ)²/Eᵢ
Où: Oᵢ = fréquences observées, Eᵢ = fréquences attendues

Application INDEX5:
- Teste l'adéquation à une distribution théorique
- Identifie les déviations significatives du modèle aléatoire

7. FORMULES DE PRÉDICTIBILITÉ
=============================

7.1 ENTROPIE CONDITIONNELLE
---------------------------
Formule: H(Y|X) = -ΣₓΣᵧ p(x,y) × log₂(p(y|x))
Où: p(x,y) = probabilité jointe, p(y|x) = probabilité conditionnelle

Application INDEX5:
- Mesure l'imprévisibilité de la prochaine combinaison
- H(Y|X) faible = forte prédictibilité

7.2 INFORMATION MUTUELLE
------------------------
Formule: I(X;Y) = H(X) - H(X|Y)
Où: H(X) = entropie de X, H(X|Y) = entropie conditionnelle

Application INDEX5:
- Mesure la dépendance entre combinaisons successives
- I élevée = forte dépendance (prédictibilité)

UTILISATION PRATIQUE POUR INDEX5
================================

Pour chaque des 18 combinaisons INDEX5:

1. Calculer CV, Gini, HHI, Shannon pour mesurer concentration
2. Appliquer test des runs pour détecter patterns
3. Calculer autocorrélation pour mesurer persistance
4. Utiliser skewness/kurtosis pour caractériser distribution
5. Appliquer tests KS et Chi² pour valider randomness
6. Calculer entropie conditionnelle pour évaluer prédictibilité

Ces formules permettront de quantifier précisément les patterns
découverts dans l'analyse des écarts-types et d'identifier
les combinaisons les plus exploitables statistiquement.

8. FORMULES AVANCÉES DE CONCENTRATION
=====================================

8.1 INDICE DE THEIL
-------------------
Formule: T = (1/n) × Σᵢ (xᵢ/μ) × ln(xᵢ/μ)
Où: xᵢ = valeurs individuelles, μ = moyenne, n = taille échantillon

Application INDEX5:
- Mesure alternative de concentration
- Sensible aux valeurs extrêmes
- T = 0 : égalité parfaite
- T élevé : forte concentration

8.2 COEFFICIENT DE CONCENTRATION (CR)
-------------------------------------
Formule: CRₖ = Σᵢ₌₁ᵏ pᵢ
Où: pᵢ = parts ordonnées par ordre décroissant, k = nombre d'éléments

Application INDEX5:
- CR₄ = concentration des 4 longueurs de runs les plus fréquentes
- Mesure la domination des longueurs principales

8.3 INDICE DE DIVERSITÉ DE BERGER-PARKER
----------------------------------------
Formule: BP = nₘₐₓ/N
Où: nₘₐₓ = effectif de la catégorie dominante, N = effectif total

Application INDEX5:
- Mesure la dominance de la longueur de run la plus fréquente
- BP élevé = forte dominance d'une longueur

9. FORMULES DE RÉGULARITÉ ET PÉRIODICITÉ
========================================

9.1 FONCTION D'AUTOCORRÉLATION PARTIELLE (PACF)
-----------------------------------------------
Formule: φₖₖ = (ρₖ - Σⱼ₌₁ᵏ⁻¹ φₖ₋₁,ⱼ × ρₖ₋ⱼ)/(1 - Σⱼ₌₁ᵏ⁻¹ φₖ₋₁,ⱼ × ρⱼ)
Où: ρₖ = autocorrélation à lag k

Application INDEX5:
- Identifie les dépendances directes entre positions
- Élimine les effets indirects des lags intermédiaires

9.2 SPECTRE DE PUISSANCE
------------------------
Formule: S(f) = |Σₜ xₜ × e^(-2πift)|²
Où: f = fréquence, t = temps, i = unité imaginaire

Application INDEX5:
- Détecte les périodicités cachées dans les séquences
- Identifie les cycles de répétition des combinaisons

9.3 COEFFICIENT DE VARIATION QUADRATIQUE
----------------------------------------
Formule: CQV = √(Σᵢ(xᵢ - μ)⁴/n) / μ²
Où: terme de 4ème ordre pour capturer variabilité extrême

Application INDEX5:
- Sensible aux variations extrêmes des longueurs de runs
- Détecte les comportements aberrants

10. FORMULES DE TRANSITION ET MARKOV
====================================

10.1 MATRICE DE TRANSITION
--------------------------
Formule: P(i,j) = nᵢⱼ/nᵢ
Où: nᵢⱼ = transitions de l'état i vers j, nᵢ = total transitions depuis i

Application INDEX5:
- Probabilités de transition entre les 18 combinaisons
- Modélise les dépendances séquentielles

10.2 ENTROPIE DE TRANSITION
---------------------------
Formule: Hₜ = -Σᵢ pᵢ × Σⱼ P(i,j) × log₂(P(i,j))
Où: pᵢ = probabilité stationnaire de l'état i

Application INDEX5:
- Mesure l'imprévisibilité des transitions
- Hₜ faible = transitions prévisibles

10.3 TEMPS DE RETOUR MOYEN
--------------------------
Formule: τᵢ = 1/πᵢ
Où: πᵢ = probabilité stationnaire de l'état i

Application INDEX5:
- Temps moyen entre deux occurrences de la même combinaison
- Identifie les combinaisons rares vs fréquentes

11. FORMULES DE DÉTECTION D'ANOMALIES
=====================================

11.1 Z-SCORE MODIFIÉ
-------------------
Formule: Mᵢ = 0.6745 × (xᵢ - médiane)/MAD
Où: MAD = médiane des déviations absolues à la médiane

Application INDEX5:
- Détection robuste des longueurs de runs anormales
- Moins sensible aux valeurs aberrantes que Z-score classique

11.2 INDICE DE ISOLATION FOREST
-------------------------------
Formule: s(x,n) = 2^(-E(h(x))/c(n))
Où: E(h(x)) = profondeur moyenne, c(n) = profondeur moyenne d'un BST

Application INDEX5:
- Détecte les combinaisons avec comportement anormal
- Identifie les patterns non-conformes

11.3 DISTANCE DE MAHALANOBIS
----------------------------
Formule: D²(x) = (x - μ)ᵀ × Σ⁻¹ × (x - μ)
Où: Σ = matrice de covariance

Application INDEX5:
- Distance multivariée pour détecter combinaisons aberrantes
- Prend en compte les corrélations entre variables

12. FORMULES DE COMPLEXITÉ
==========================

12.1 COMPLEXITÉ DE LEMPEL-ZIV
-----------------------------
Formule: C(s) = |{w : w est un facteur de s}|
Où: s = séquence, w = sous-séquences distinctes

Application INDEX5:
- Mesure la complexité algorithmique des séquences
- C élevée = séquence complexe/aléatoire
- C faible = séquence simple/répétitive

12.2 DIMENSION DE CORRÉLATION
-----------------------------
Formule: D₂ = lim(r→0) log(C(r))/log(r)
Où: C(r) = fonction de corrélation

Application INDEX5:
- Mesure la dimensionnalité effective du système
- Détecte les attracteurs étranges

13. APPLICATIONS SPÉCIFIQUES LUPASCO
====================================

13.1 INDICE DE LUPASCO (NOUVEAU)
-------------------------------
Formule: IL = (N_impair5_isolé + N_TIE_isolé)/(N_total) × 100%
Où: N_impair5_isolé = runs de longueur 1 avec impair_5

Application INDEX5:
- Mesure spécifique du comportement Lupasco
- Quantifie la règle d'anti-persistance découverte

13.2 COEFFICIENT DE PRÉDICTIBILITÉ LUPASCO
------------------------------------------
Formule: CPL = 1 - H_conditionnel/H_maximum
Où: H_conditionnel = entropie sachant l'état précédent

Application INDEX5:
- Mesure la prédictibilité basée sur les règles Lupasco
- CPL élevé = forte prédictibilité

13.3 SCORE DE CONFORMITÉ THÉORIQUE
----------------------------------
Formule: SCT = 1 - |L_observé - L_théorique|/L_théorique
Où: L = longueur moyenne des runs

Application INDEX5:
- Mesure l'écart à la théorie géométrique
- SCT proche de 1 = comportement conforme
- SCT faible = comportement non-aléatoire

SYNTHÈSE POUR EXPLOITATION INDEX5
=================================

PRIORITÉ 1 - FORMULES ESSENTIELLES:
- Coefficient de Variation (comparaison des 18 combinaisons)
- Test des Runs (détection patterns)
- Autocorrélation lag-1 (mesure persistance)
- Entropie de Shannon (mesure prédictibilité)

PRIORITÉ 2 - FORMULES AVANCÉES:
- Indice de Gini (concentration)
- Matrice de transition (dépendances)
- Indice de Lupasco (spécifique au système)

PRIORITÉ 3 - FORMULES SPÉCIALISÉES:
- Tests de normalité (KS, Chi²)
- Détection d'anomalies (Z-score modifié)
- Complexité (Lempel-Ziv)

Ces 40+ formules offrent un arsenal complet pour analyser
quantitativement les 18 combinaisons INDEX5 et développer
des stratégies prédictives optimales.
