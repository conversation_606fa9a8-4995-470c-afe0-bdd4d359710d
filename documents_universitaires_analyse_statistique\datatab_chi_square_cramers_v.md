# DATATAB - CHI-<PERSON>QUARE TEST ET CRAMÉR'S V

**Source :** https://datatab.net/tutorial/chi-square-test

## MÉTHODES SPÉCIFIQUES

### CHI-SQUARE TEST OF INDEPENDENCE
- **Objectif** : Déterminer si deux variables catégorielles sont indépendantes
- **Hypothèses** :
  - H0 : Les variables sont indépendantes
  - H1 : Il existe une dépendance
- **Conditions d'application** :
  - Effectifs attendus ≥ 5 dans chaque cellule
  - Observations indépendantes

### CRAMÉR'S V - MESURE D'EFFET
- **Formule** : V = √(χ²/(n × min(r-1, c-1)))
- **Interprétation** :
  - 0 = indépendance parfaite
  - 1 = association parfaite
  - 0.1 = effet faible
  - 0.3 = effet moyen
  - 0.5 = effet fort

### CALCUL PRATIQUE
```
1. Construire tableau de contingence
2. Calculer effectifs attendus
3. Calculer χ² = Σ[(Observé - Attendu)²/Attendu]
4. Calculer V de Cramér
5. Interpréter avec degrés de liberté
```

## APPLICATION LUPASCO

### POUR INDEX1-INDEX2
- Test d'indépendance synchrone
- Calcul V de Cramér pour quantifier l'association
- Validation des conditions d'application

### POUR VALIDATION TEMPORELLE
- Test INDEX2(t) → INDEX1(t+1)
- Mesure de l'effet de la règle Lupasco
- Comparaison avec indépendance théorique
