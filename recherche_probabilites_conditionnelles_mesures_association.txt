RECHERCHE SUR LES PROBABILITÉS CONDITIONNELLES ET MESURES D'ASSOCIATION
========================================================================

Date: 2025-06-17
Contexte: Analyse des relations causales INDEX1 → INDEX2 → INDEX3 dans le système Lupasco

## 1. PROBABILITÉS CONDITIONNELLES - CONCEPTS CLÉS

### 1.1 Définition
P(A|B) = P(A ∩ B) / P(B)
- Probabilité de A sachant que B s'est produit
- Mesure la dépendance entre variables

### 1.2 Application au système Lupasco
- P(INDEX2|INDEX1) : Comment INDEX1 influence INDEX2
- P(INDEX3|INDEX1, INDEX2) : Comment la combinaison INDEX1+INDEX2 influence INDEX3

## 2. TESTS D'INDÉPENDANCE STATISTIQUE

### 2.1 Test du Chi² (χ²)
- **Usage** : Tester l'indépendance entre variables catégorielles
- **H0** : Les variables sont indépendantes
- **H1** : Il existe une dépendance
- **Formule** : χ² = Σ[(Observé - Attendu)²/Attendu]

### 2.2 Application pour Lupasco
- Test χ² pour INDEX1 × INDEX2
- Test χ² pour (INDEX1, INDEX2) × INDEX3
- P-value < 0.05 = dépendance significative

## 3. MESURES D'ASSOCIATION POUR VARIABLES CATÉGORIELLES

### 3.1 V de Cramér
- **Range** : 0 à 1 (0 = indépendance, 1 = association parfaite)
- **Formule** : V = √(χ²/(n × min(r-1, c-1)))
- **Avantage** : Normalisé, comparable entre tableaux de tailles différentes

### 3.2 Coefficient de Contingence
- **Range** : 0 à √((k-1)/k) où k = min(lignes, colonnes)
- **Usage** : Mesure alternative d'association

### 3.3 Coefficient Phi (φ)
- **Usage** : Pour tableaux 2×2 uniquement
- **Range** : -1 à +1

## 4. ANALYSE CAUSALE - CONCEPTS AVANCÉS

### 4.1 Chaînes de Causalité
- INDEX2 → INDEX1 (INDEX2 cause INDEX1)
- INDEX2 → INDEX3 (INDEX2 cause INDEX3)
- INDEX1 → INDEX3 (influence indirecte via INDEX2)

### 4.2 Médiation Statistique
- Effet direct : INDEX1 → INDEX3
- Effet indirect : INDEX1 → INDEX2 → INDEX3
- Effet total = Effet direct + Effet indirect

## 5. MÉTRIQUES SPÉCIFIQUES POUR LUPASCO

### 5.1 Tableaux de Contingence
```
INDEX1 × INDEX2:
           pair_4  pair_6  impair_5
SYNC       n11     n12     n13
DESYNC     n21     n22     n23
```

### 5.2 Probabilités Conditionnelles à Calculer
- P(pair_4|SYNC), P(pair_6|SYNC), P(impair_5|SYNC)
- P(pair_4|DESYNC), P(pair_6|DESYNC), P(impair_5|DESYNC)
- P(PLAYER|SYNC,pair_4), P(BANKER|SYNC,pair_4), etc.

### 5.3 Tests de Dépendance
- χ² test pour INDEX1-INDEX2
- χ² test pour (INDEX1,INDEX2)-INDEX3
- V de Cramér pour quantifier la force d'association

## 6. MÉTRIQUES D'INFLUENCE CAUSALE

### 6.1 Influence de INDEX2 sur INDEX3
- Comparer P(INDEX3|INDEX2) vs P(INDEX3)
- Mesurer l'écart par rapport à l'indépendance

### 6.2 Influence de INDEX1 sur INDEX3
- Effet direct : P(INDEX3|INDEX1) vs P(INDEX3)
- Effet via INDEX2 : P(INDEX3|INDEX1,INDEX2) vs P(INDEX3|INDEX2)

### 6.3 Métriques de Prédiction
- Précision de argmax(P(INDEX2|INDEX1))
- Gain d'information de INDEX1 pour prédire INDEX2
- Gain d'information de (INDEX1,INDEX2) pour prédire INDEX3

## 7. IMPLÉMENTATION RECOMMANDÉE

### 7.1 Étapes d'Analyse
1. Construire tableaux de contingence
2. Calculer toutes les probabilités conditionnelles
3. Effectuer tests χ² d'indépendance
4. Calculer V de Cramér pour quantifier associations
5. Mesurer gains d'information et capacités prédictives

### 7.2 Outputs Attendus
- Matrices de probabilités conditionnelles
- P-values des tests d'indépendance
- Coefficients V de Cramér
- Métriques d'influence causale
- Capacités prédictives du système

## 8. INTERPRÉTATION CAUSALE

### 8.1 Relations Causales Hypothétiques
- INDEX2 (cartes) → INDEX1 (SYNC/DESYNC)
- INDEX2 (cartes) → INDEX3 (PLAYER/BANKER)
- INDEX1 influence INDEX3 via INDEX2

### 8.2 Validation Statistique
- Si P(INDEX2|INDEX1) ≠ P(INDEX2) → INDEX1 influence INDEX2
- Si P(INDEX3|INDEX1,INDEX2) ≠ P(INDEX3|INDEX2) → INDEX1 influence INDEX3 au-delà de INDEX2
- Si P(INDEX3|INDEX2) ≠ P(INDEX3) → INDEX2 influence INDEX3

Cette approche permettra de quantifier précisément les relations causales
dans le système Lupasco sur les 100 000 parties générées.
