# COURSERA - TIME SERIES ANALYSIS

**Source :** https://www.coursera.org/articles/time-series-analysis

## CONCEPTS FONDAMENTAUX

### DÉFINITION SÉRIE TEMPORELLE
- **Données ordonnées dans le temps** : observations séquentielles
- **Dépendance temporelle** : valeurs actuelles influencées par le passé
- **Patterns identifiables** : tendances, saisonnalité, cycles

### MÉTHODES D'ANALYSE
- **Analyse de tendance** : évolution à long terme
- **Détection de patterns** : répétitions, cycles
- **Autocorrélation** : corrélation avec valeurs passées
- **Prédiction** : estimation de valeurs futures

### OUTILS STATISTIQUES
- **Fonctions d'autocorrélation** : mesure de la mémoire
- **Tests de stationnarité** : propriétés constantes dans le temps
- **Modèles ARIMA** : autorégressifs intégrés moyennes mobiles
- **Validation de modèles** : tests de résidus

## APPLICATIONS LUPASCO

### POUR INDEX1 (SYNC/DESYNC)
- **Autocorrélation forte (0.3878)** : mémoire temporelle confirmée
- **Patterns non-aléatoires** : structure déterministe
- **Prédictibilité** : basée sur valeurs passées

### VALIDATION RÈGLE LUPASCO
- **Analyse causale temporelle** : INDEX2(t) → INDEX1(t+1)
- **Tests de dépendance** : validation de la règle
- **Mesure de performance** : précision prédictive

### MÉTHODES RECOMMANDÉES
- Tests d'autocorrélation avec intervalles de confiance
- Analyse de la fonction d'autocorrélation partielle
- Validation par échantillons temporels séparés
