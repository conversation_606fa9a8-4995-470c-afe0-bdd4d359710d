Exemple :

- Brulage (debut de partie) : un nombre pair ou impair est brûlé : renseigne l'index 1 pour la prochaine main : SYNC ou DESYNC (si pair lors du brulage alors SYNC, si impair lors du brulage alors DESYNC).
- main 1 : d<PERSON><PERSON><PERSON> en SYNC ou DESYNC (si pair ou impair au brulage).
Disons que la main 1 démarre en SYNC.
Nous ne connaissons pas encore le résultat.
Nous n'avons pas encore assez de valeurs dans les index pour effectuer des calculs de probabilité. Nous attendons le premier résultat de la main 1.
- 4 cartes sont distribuées, <PERSON><PERSON> gagne. Nous avons donc pour la main 1 :
SYNC pair_4 BANKER.

- Nous savons alors que main 2 commence en état SYNC.

Les cartes ne sont pas encore distribuées.
Le système calcule les probabilités : 

P(INDEX2|INDEX1)
INDEX2 dispose de trois valeurs : pair_4 ou pair_6 ou impair_5.
Donc : 3 calculs de probabilité.

P(INDEX2|INDEX1) est en fait : 
P(pair_4|INDEX1)
P(pair_6|INDEX1)
P(impair_5|INDEX1)

Comme pair_4 est déjà apparu une fois à la main précédente et que :
le poids accordé à l'une des 3 valeurs (de l'INDEX2) diminue en fonction de n consécutifs de valeur de INDEX2
Plus n consécutif est grand, plus le poids accordé à valeur de l'INDEX2 diminue.

Alors : pair_4 aura un poids plus petit. 

De plus : comme pair_4 est PAIR et que pair_6 est PAIR, alors il est logique de impair_5 ait le poids le plus élevé puisqu'un PAIR est apparu lors de la main 1.
Donc : P(impair_5|INDEX1) sera most_likely et aura la probabilité la plus élevée.

Ensuite : 
Maintenant que nous avons la prédiction de l'INDEX2, 
Nous utilisons cette prédiction pour le calcul de probabilité de l'INDEX3 :

P(PLAYER| (P(impair_5|INDEX1) ) =
P(BANKER| (P(impair_5|INDEX1) ) =
P(TIE| (P(impair_5|INDEX1) ) =

POST SCRIPTUM : Cette réflexion de ma part n'est peut être pas cohérente :
"De plus : comme pair_4 est PAIR et que pair_6 est PAIR, alors il est logique de impair_5 ait le poids le plus élevé puisqu'un PAIR est apparu lors de la main 1.
Donc : P(impair_5|INDEX1) sera most_likely et aura la probabilité la plus élevée."

CORRECTION APRÈS RECHERCHES : Cette logique contradictoire est CORRECTE selon Lupasco !
- pair_4 et pair_6 sont tous deux PAIR
- Selon la logique du contradictoire de Lupasco, après un PAIR (main 1), l'IMPAIR (impair_5) devient plus probable
- Cette pondération respecte le principe d'actualisation → potentialisation du contradictoire

Le système sélectionne automatiquement la probabilité la plus élevée de P(INDEX3| (P(INDEX2|1) )

NOTE IMPORTANTE : il faut faire des recherches sur internet pour comprendre comment faire le calcul de :
- P(INDEX2|INDEX1) : Probabilité qu'à la main suivante se produise une des 3 valeurs de l'INDEX2 sachant l'INDEX1.
- P(INDEX3| (P(INDEX2|1) )

Pour P(INDEX3| (P(INDEX2|1) ) ne faut-il pas en fait multiplier la probabilité de P(INDEX2|INDEX1) par la probabilité P(INDEX3|INDEX1) ?
Nous voulons trouver la probabilité que Banker ou Player ou Tie se produise SACHANT la probabilité la plus élevée que une des 3 valeurs se produise SACHANT l'INDEX1.

RÉPONSES APRÈS RECHERCHES :

1. P(INDEX2|INDEX1) : FORMULE CORRECTE
   P(pair_4|SYNC) = count(pair_4 ET SYNC) / count(SYNC)
   P(pair_6|SYNC) = count(pair_6 ET SYNC) / count(SYNC)
   P(impair_5|SYNC) = count(impair_5 ET SYNC) / count(SYNC)
   → Calcul par comptage sur données historiques

2. FORMULE CORRECTE : P(INDEX3|INDEX1, INDEX2_most_likely)
   = count(INDEX3 ET INDEX2_most_likely ET INDEX1) / count(INDEX2_most_likely ET INDEX1)

   EXPLICATION : On compte les séquences historiques où INDEX1, INDEX2_most_likely et INDEX3
   se sont produits ensemble, divisé par les séquences où INDEX1 et INDEX2_most_likely
   se sont produits ensemble.

3. SYSTÈME CORRECT :
   Étape 1 : Calculer P(INDEX2|INDEX1) pour les 3 valeurs
   Étape 2 : Identifier most_likely = argmax(P(INDEX2|INDEX1))
   Étape 3 : Calculer P(INDEX3|INDEX1, INDEX2_most_likely)

   FORMULE FINALE : P(INDEX3|INDEX1, argmax(P(INDEX2|INDEX1)))

