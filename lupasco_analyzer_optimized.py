# -*- coding: utf-8 -*-
"""
ANALYSEUR LUPASCO OPTIMISÉ AVEC FORMULES MATHÉMATIQUES EXACTES
==============================================================

Implémentation optimisée pour l'analyse du système Lupasco avec :
- Formules mathématiques exactes vérifiées
- Gestion mémoire efficace pour gros datasets (7GB+)
- Tests statistiques complets
- Prédictions basées sur les probabilités conditionnelles

Auteur: Assistant IA Augment
Date: 2025-06-18
"""

import numpy as np
import pandas as pd
import json
from pathlib import Path
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact
import warnings
from typing import Dict, List, Tuple, Optional, Union
import gc
from formules_mathematiques_exactes import (
    gini_coefficient, shannon_entropy_from_data, 
    autocorrelation_function, coefficient_of_variation,
    runs_test, lupasco_entropy_analysis
)

warnings.filterwarnings('ignore', category=RuntimeWarning)


class LupascoAnalyzer:
    """
    Analyseur optimisé pour le système Lupasco avec formules mathématiques exactes.
    """
    
    def __init__(self, chunk_size: int = 10000):
        """
        Initialise l'analyseur Lupasco.
        
        Args:
            chunk_size: Taille des chunks pour traitement par blocs (gestion mémoire)
        """
        self.chunk_size = chunk_size
        self.data = None
        self.results = {}
        
    def load_data_efficient(self, file_path: Union[str, Path]) -> None:
        """
        Charge les données de manière efficace en mémoire.
        
        Args:
            file_path: Chemin vers le fichier JSON
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Fichier non trouvé: {file_path}")
        
        print(f"Chargement des données depuis {file_path}...")
        
        # Chargement par chunks pour économiser la mémoire
        chunks = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Conversion en DataFrame avec optimisation mémoire
        df = pd.DataFrame(data)
        
        # Optimisation des types de données
        for col in df.columns:
            if df[col].dtype == 'object':
                try:
                    df[col] = pd.Categorical(df[col])
                except:
                    pass
            elif df[col].dtype == 'int64':
                df[col] = pd.to_numeric(df[col], downcast='integer')
            elif df[col].dtype == 'float64':
                df[col] = pd.to_numeric(df[col], downcast='float')
        
        self.data = df
        print(f"Données chargées: {len(df)} lignes, {len(df.columns)} colonnes")
        print(f"Utilisation mémoire: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
    def calculate_lupasco_probabilities(self) -> Dict:
        """
        Calcule les probabilités conditionnelles exactes du système Lupasco.
        
        Formules utilisées:
        1. P(INDEX1(t+1)|INDEX2(t)) - Règle déterministe de Lupasco
        2. P(INDEX2(t+1)|INDEX1(t+1)) - Probabilité conditionnelle synchrone
        3. P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) - Prédiction finale
        
        Returns:
            Dict: Probabilités conditionnelles calculées
        """
        if self.data is None:
            raise ValueError("Aucune donnée chargée. Utilisez load_data_efficient() d'abord.")
        
        results = {}
        
        # Préparation des données
        df = self.data.copy()
        
        # Vérification des colonnes nécessaires
        required_cols = ['INDEX1', 'INDEX2', 'INDEX3']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Colonnes manquantes: {missing_cols}")
        
        # 1. P(INDEX1(t+1)|INDEX2(t)) - Règle de Lupasco
        print("Calcul de P(INDEX1(t+1)|INDEX2(t))...")
        
        # Créer les décalages temporels
        df['INDEX1_next'] = df['INDEX1'].shift(-1)
        df['INDEX2_prev'] = df['INDEX2'].shift(1)
        
        # Supprimer les lignes avec NaN
        df_clean = df.dropna(subset=['INDEX1_next', 'INDEX2_prev'])
        
        # Table de contingence INDEX2(t) vs INDEX1(t+1)
        contingency_12 = pd.crosstab(df_clean['INDEX2_prev'], df_clean['INDEX1_next'])
        
        # Probabilités conditionnelles P(INDEX1(t+1)|INDEX2(t))
        prob_index1_given_index2 = contingency_12.div(contingency_12.sum(axis=1), axis=0)
        results['P_INDEX1_given_INDEX2'] = prob_index1_given_index2.to_dict()
        
        # Test chi-carré pour indépendance
        chi2_stat, p_val_chi2, dof, expected = chi2_contingency(contingency_12)
        results['chi2_test_INDEX1_INDEX2'] = {
            'statistic': chi2_stat,
            'p_value': p_val_chi2,
            'degrees_of_freedom': dof
        }
        
        # 2. P(INDEX2(t+1)|INDEX1(t+1)) - Probabilité synchrone
        print("Calcul de P(INDEX2(t+1)|INDEX1(t+1))...")
        
        df['INDEX2_next'] = df['INDEX2'].shift(-1)
        df_sync = df.dropna(subset=['INDEX1_next', 'INDEX2_next'])
        
        contingency_sync = pd.crosstab(df_sync['INDEX1_next'], df_sync['INDEX2_next'])
        prob_index2_given_index1 = contingency_sync.div(contingency_sync.sum(axis=1), axis=0)
        results['P_INDEX2_given_INDEX1'] = prob_index2_given_index1.to_dict()
        
        # 3. P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) - Prédiction finale
        print("Calcul de P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1))...")
        
        df['INDEX3_next'] = df['INDEX3'].shift(-1)
        df_final = df.dropna(subset=['INDEX1_next', 'INDEX2_next', 'INDEX3_next'])
        
        # Groupement par INDEX1 et INDEX2
        grouped_probs = {}
        for (idx1, idx2), group in df_final.groupby(['INDEX1_next', 'INDEX2_next']):
            index3_counts = group['INDEX3_next'].value_counts()
            index3_probs = index3_counts / index3_counts.sum()
            grouped_probs[(idx1, idx2)] = index3_probs.to_dict()
        
        results['P_INDEX3_given_INDEX1_INDEX2'] = grouped_probs
        
        # Calcul de la précision de prédiction
        predictions = []
        actuals = []
        
        for _, row in df_final.iterrows():
            idx1, idx2, actual_idx3 = row['INDEX1_next'], row['INDEX2_next'], row['INDEX3_next']
            
            if (idx1, idx2) in grouped_probs:
                probs = grouped_probs[(idx1, idx2)]
                predicted_idx3 = max(probs.keys(), key=lambda k: probs[k])
                predictions.append(predicted_idx3)
                actuals.append(actual_idx3)
        
        if predictions:
            accuracy = np.mean(np.array(predictions) == np.array(actuals))
            results['prediction_accuracy'] = accuracy
        
        return results
    
    def analyze_lupasco_patterns(self) -> Dict:
        """
        Analyse complète des patterns Lupasco avec formules exactes.
        
        Returns:
            Dict: Résultats de l'analyse des patterns
        """
        if self.data is None:
            raise ValueError("Aucune donnée chargée.")
        
        results = {}
        df = self.data
        
        # 1. Analyse de l'autocorrélation INDEX1
        print("Analyse de l'autocorrélation INDEX1...")
        index1_numeric = pd.Categorical(df['INDEX1']).codes
        autocorr_index1 = autocorrelation_function(index1_numeric, max_lag=10)
        results['autocorrelation_INDEX1'] = autocorr_index1.tolist()
        
        # 2. Test des runs pour randomness
        print("Test des runs pour INDEX1...")
        runs_result = runs_test(index1_numeric)
        results['runs_test_INDEX1'] = runs_result
        
        # 3. Entropie de Shannon pour chaque index
        print("Calcul des entropies...")
        entropy_results = lupasco_entropy_analysis(
            df['INDEX1'].values,
            df['INDEX2'].values, 
            df['INDEX3'].values
        )
        results['entropy_analysis'] = entropy_results
        
        # 4. Coefficient de Gini pour dispersion
        print("Calcul des coefficients de Gini...")
        for col in ['INDEX1', 'INDEX2', 'INDEX3']:
            if df[col].dtype.kind in 'biufc':
                gini_val = gini_coefficient(df[col].values)
                results[f'gini_{col}'] = gini_val
        
        # 5. Coefficient de variation
        print("Calcul des coefficients de variation...")
        for col in df.select_dtypes(include=[np.number]).columns:
            cv_val = coefficient_of_variation(df[col].values)
            results[f'cv_{col}'] = cv_val
        
        return results
    
    def predict_next_hand(self, current_index1: str, current_index2: str) -> Dict:
        """
        Prédit la prochaine main basée sur INDEX1 et INDEX2 actuels.
        
        Args:
            current_index1: Valeur actuelle d'INDEX1
            current_index2: Valeur actuelle d'INDEX2
            
        Returns:
            Dict: Prédictions avec probabilités
        """
        if 'P_INDEX3_given_INDEX1_INDEX2' not in self.results:
            raise ValueError("Calculs de probabilités non effectués. Lancez calculate_lupasco_probabilities() d'abord.")
        
        probs = self.results['P_INDEX3_given_INDEX1_INDEX2']
        
        if (current_index1, current_index2) in probs:
            index3_probs = probs[(current_index1, current_index2)]
            
            # Prédiction = classe avec probabilité maximale
            predicted_index3 = max(index3_probs.keys(), key=lambda k: index3_probs[k])
            max_prob = index3_probs[predicted_index3]
            
            return {
                'predicted_INDEX3': predicted_index3,
                'confidence': max_prob,
                'all_probabilities': index3_probs,
                'input_state': (current_index1, current_index2)
            }
        else:
            return {
                'predicted_INDEX3': None,
                'confidence': 0.0,
                'all_probabilities': {},
                'input_state': (current_index1, current_index2),
                'error': 'Combinaison non trouvée dans les données historiques'
            }
    
    def generate_comprehensive_report(self, output_file: Optional[str] = None) -> Dict:
        """
        Génère un rapport complet de l'analyse Lupasco.
        
        Args:
            output_file: Fichier de sortie optionnel pour sauvegarder le rapport
            
        Returns:
            Dict: Rapport complet
        """
        print("Génération du rapport complet...")
        
        # Calculs principaux
        prob_results = self.calculate_lupasco_probabilities()
        pattern_results = self.analyze_lupasco_patterns()
        
        # Consolidation des résultats
        self.results.update(prob_results)
        self.results.update(pattern_results)
        
        # Métadonnées
        self.results['metadata'] = {
            'total_hands': len(self.data),
            'analysis_date': pd.Timestamp.now().isoformat(),
            'data_columns': list(self.data.columns),
            'memory_usage_mb': self.data.memory_usage(deep=True).sum() / 1024**2
        }
        
        # Sauvegarde optionnelle
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, default=str)
            print(f"Rapport sauvegardé dans {output_file}")
        
        return self.results
    
    def cleanup_memory(self):
        """Nettoie la mémoire en supprimant les données temporaires."""
        if hasattr(self, 'data') and self.data is not None:
            del self.data
        gc.collect()
        print("Mémoire nettoyée.")


# ============================================================================
# FONCTIONS UTILITAIRES
# ============================================================================

def quick_lupasco_analysis(file_path: str, output_file: str = None) -> Dict:
    """
    Analyse rapide complète du système Lupasco.
    
    Args:
        file_path: Chemin vers le fichier de données JSON
        output_file: Fichier de sortie optionnel
        
    Returns:
        Dict: Résultats de l'analyse
    """
    analyzer = LupascoAnalyzer()
    
    try:
        analyzer.load_data_efficient(file_path)
        results = analyzer.generate_comprehensive_report(output_file)
        return results
    
    finally:
        analyzer.cleanup_memory()


if __name__ == "__main__":
    # Exemple d'utilisation
    print("Analyseur Lupasco Optimisé - Test de validation")
    print("=" * 50)
    
    # Test avec données simulées
    np.random.seed(42)
    test_data = {
        'INDEX1': np.random.choice(['SYNC', 'DESYNC'], 1000),
        'INDEX2': np.random.choice(['pair_4', 'pair_6', 'impair_5'], 1000),
        'INDEX3': np.random.choice(['PLAYER', 'BANKER', 'TIE'], 1000, p=[0.45, 0.45, 0.1])
    }
    
    analyzer = LupascoAnalyzer()
    analyzer.data = pd.DataFrame(test_data)
    
    # Test des fonctions principales
    print("Test des probabilités conditionnelles...")
    prob_results = analyzer.calculate_lupasco_probabilities()
    print(f"Précision de prédiction: {prob_results.get('prediction_accuracy', 'N/A'):.4f}")

    # Stocker les résultats pour la prédiction
    analyzer.results = prob_results

    print("\nTest de prédiction...")
    prediction = analyzer.predict_next_hand('SYNC', 'pair_4')
    print(f"Prédiction: {prediction['predicted_INDEX3']} (confiance: {prediction['confidence']:.4f})")
    
    print("\nAnalyseur Lupasco prêt à l'utilisation !")
