#!/usr/bin/env python3
"""
ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO - VERSION SIMPLIFIÉE
================================================================

Version simplifiée gardant uniquement les méthodes d'analyse des runs
et de génération du rapport de base.

Méthodes conservées :
- Analyse des runs (INDEX1, INDEX2, INDEX3)
- Calculs statistiques de base (entropie, autocorrélation, runs test)
- Génération du rapport principal

Date: 2025-06-18
"""

import json
import ijson
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime
from scipy import stats
import math

# Vérification matplotlib (optionnel)
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences Lupasco - Version simplifiée
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur
        
        Args:
            fichier_json: Chemin vers le fichier JSON des données Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        self.nb_parties_total = 0
        
        print(f"🔬 Analyseur Lupasco initialisé")
        print(f"📁 Fichier source : {fichier_json}")

    def charger_donnees(self):
        """Charge et extrait les données du fichier JSON volumineux (7GB) avec streaming"""
        print("\n📥 CHARGEMENT DES DONNÉES")
        print("-" * 30)
        
        try:
            # Essayer le chargement avec streaming pour gros fichiers
            self._charger_avec_streaming()
        except Exception as e:
            print(f"⚠️  Streaming échoué ({e}), tentative chargement standard...")
            try:
                self._charger_standard()
            except Exception as e2:
                raise Exception(f"Échec chargement streaming ET standard: {e2}")
        
        # Extraire les séquences
        self._extraire_sequences()
        
        print(f"✅ Données chargées : {self.nb_parties_total:,} parties")
        print(f"📊 Séquences extraites : {list(self.sequences.keys())}")

    def _charger_avec_streaming(self):
        """Charge le fichier JSON avec streaming ijson (pour fichiers 7GB)"""
        print("   Mode streaming (ijson)...")
        
        self.donnees = []
        
        with open(self.fichier_json, 'rb') as f:
            # Parser streaming pour les gros fichiers
            parser = ijson.items(f, 'item')
            
            count = 0
            for partie in parser:
                self.donnees.append(partie)
                count += 1
                
                # Affichage du progrès
                if count % 10000 == 0:
                    print(f"      Parties chargées : {count:,}")
        
        self.nb_parties_total = len(self.donnees)
        print(f"   ✅ Streaming terminé : {self.nb_parties_total:,} parties")

    def _charger_standard(self):
        """Chargement standard pour fichiers plus petits"""
        print("   Mode standard (json)...")
        
        with open(self.fichier_json, 'r', encoding='utf-8') as f:
            self.donnees = json.load(f)
        
        self.nb_parties_total = len(self.donnees)
        print(f"   ✅ Chargement standard terminé : {self.nb_parties_total:,} parties")

    def _extraire_sequences(self):
        """Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties"""
        print("   Extraction des séquences...")
        
        # Initialiser les listes de séquences
        index1_seq = []
        index2_seq = []
        index3_seq = []
        
        for partie in self.donnees:
            # INDEX1 : SYNC/DESYNC
            idx1 = partie.get('INDEX1')
            if idx1:
                index1_seq.append(idx1)
            
            # INDEX2 : pair_4/pair_6/impair_5
            idx2 = partie.get('INDEX2')
            if idx2:
                index2_seq.append(idx2)
            
            # INDEX3 : PLAYER/BANKER (exclure TIE)
            idx3 = partie.get('INDEX3')
            if idx3 in ['PLAYER', 'BANKER']:
                index3_seq.append(idx3)
        
        # Stocker les séquences
        self.sequences = {
            'INDEX1': index1_seq,
            'INDEX2': index2_seq,
            'INDEX3': index3_seq
        }
        
        print(f"      INDEX1 : {len(index1_seq):,} éléments")
        print(f"      INDEX2 : {len(index2_seq):,} éléments")
        print(f"      INDEX3 : {len(index3_seq):,} éléments")
