FORMULES MATHÉMATIQUES EXACTES POUR LES PROBABILITÉS CONDITIONNELLES
====================================================================

Date: 2025-06-17
Contexte: Système Lupasco - Calculs exacts des probabilités conditionnelles
Basé sur: Recherches académiques et définitions mathématiques standard

## 1. DÉFINITION FONDAMENTALE DE LA PROBABILITÉ CONDITIONNELLE

### Formule de base :
P(A|B) = P(A ∩ B) / P(B)

Où :
- P(A|B) = Probabilité de A sachant B
- P(A ∩ B) = Probabilité de l'intersection de A et B
- P(B) = Probabilité marginale de B
- Condition : P(B) > 0

## 2. APPLICATION AU SYSTÈME LUPASCO

### 2.1 Calcul de P(INDEX2|INDEX1)

**Formule générale :**
P(INDEX2 = i2|INDEX1 = i1) = count(INDEX2 = i2 ET INDEX1 = i1) / count(INDEX1 = i1)

**Applications spécifiques :**

P(pair_4|SYNC) = count(pair_4 ET SYNC) / count(SYNC)
P(pair_6|SYNC) = count(pair_6 ET SYNC) / count(SYNC)
P(impair_5|SYNC) = count(impair_5 ET SYNC) / count(SYNC)

P(pair_4|DESYNC) = count(pair_4 ET DESYNC) / count(DESYNC)
P(pair_6|DESYNC) = count(pair_6 ET DESYNC) / count(DESYNC)
P(impair_5|DESYNC) = count(impair_5 ET DESYNC) / count(DESYNC)

**Vérification :**
Pour chaque valeur d'INDEX1 :
P(pair_4|INDEX1) + P(pair_6|INDEX1) + P(impair_5|INDEX1) = 1

### 2.2 Calcul de P(INDEX3|INDEX1, INDEX2)

**Formule générale :**
P(INDEX3 = i3|INDEX1 = i1, INDEX2 = i2) = 
    count(INDEX3 = i3 ET INDEX2 = i2 ET INDEX1 = i1) / count(INDEX2 = i2 ET INDEX1 = i1)

**Applications spécifiques :**

P(PLAYER|SYNC, pair_4) = count(PLAYER ET pair_4 ET SYNC) / count(pair_4 ET SYNC)
P(BANKER|SYNC, pair_4) = count(BANKER ET pair_4 ET SYNC) / count(pair_4 ET SYNC)

P(PLAYER|SYNC, pair_6) = count(PLAYER ET pair_6 ET SYNC) / count(pair_6 ET SYNC)
P(BANKER|SYNC, pair_6) = count(BANKER ET pair_6 ET SYNC) / count(pair_6 ET SYNC)

P(PLAYER|SYNC, impair_5) = count(PLAYER ET impair_5 ET SYNC) / count(impair_5 ET SYNC)
P(BANKER|SYNC, impair_5) = count(BANKER ET impair_5 ET SYNC) / count(impair_5 ET SYNC)

P(PLAYER|DESYNC, pair_4) = count(PLAYER ET pair_4 ET DESYNC) / count(pair_4 ET DESYNC)
P(BANKER|DESYNC, pair_4) = count(BANKER ET pair_4 ET DESYNC) / count(pair_4 ET DESYNC)

P(PLAYER|DESYNC, pair_6) = count(PLAYER ET pair_6 ET DESYNC) / count(pair_6 ET DESYNC)
P(BANKER|DESYNC, pair_6) = count(BANKER ET pair_6 ET DESYNC) / count(pair_6 ET DESYNC)

P(PLAYER|DESYNC, impair_5) = count(PLAYER ET impair_5 ET DESYNC) / count(impair_5 ET DESYNC)
P(BANKER|DESYNC, impair_5) = count(BANKER ET impair_5 ET DESYNC) / count(impair_5 ET DESYNC)

**Vérification :**
Pour chaque combinaison (INDEX1, INDEX2) :
P(PLAYER|INDEX1, INDEX2) + P(BANKER|INDEX1, INDEX2) = 1

## 3. SYSTÈME DE PRÉDICTION AVEC ARGMAX

### 3.1 Étape 1 : Calcul de toutes les probabilités P(INDEX2|INDEX1)

Pour INDEX1 = SYNC :
- P(pair_4|SYNC)
- P(pair_6|SYNC)  
- P(impair_5|SYNC)

Pour INDEX1 = DESYNC :
- P(pair_4|DESYNC)
- P(pair_6|DESYNC)
- P(impair_5|DESYNC)

### 3.2 Étape 2 : Identification de INDEX2_most_likely

**Formule argmax :**
INDEX2_most_likely(INDEX1) = argmax_{i2 ∈ {pair_4, pair_6, impair_5}} P(i2|INDEX1)

**Applications :**
INDEX2_most_likely(SYNC) = argmax{P(pair_4|SYNC), P(pair_6|SYNC), P(impair_5|SYNC)}
INDEX2_most_likely(DESYNC) = argmax{P(pair_4|DESYNC), P(pair_6|DESYNC), P(impair_5|DESYNC)}

### 3.3 Étape 3 : Calcul de P(INDEX3|INDEX1, INDEX2_most_likely)

**Formule finale :**
P(INDEX3|INDEX1, INDEX2_most_likely) = 
    count(INDEX3 ET INDEX2_most_likely ET INDEX1) / count(INDEX2_most_likely ET INDEX1)

**Applications :**
Si INDEX2_most_likely(SYNC) = pair_4, alors :
P(PLAYER|SYNC, pair_4) = count(PLAYER ET pair_4 ET SYNC) / count(pair_4 ET SYNC)
P(BANKER|SYNC, pair_4) = count(BANKER ET pair_4 ET SYNC) / count(pair_4 ET SYNC)

Si INDEX2_most_likely(DESYNC) = pair_6, alors :
P(PLAYER|DESYNC, pair_6) = count(PLAYER ET pair_6 ET DESYNC) / count(pair_6 ET DESYNC)
P(BANKER|DESYNC, pair_6) = count(BANKER ET pair_6 ET DESYNC) / count(pair_6 ET DESYNC)

## 4. FORMULES DE VÉRIFICATION

### 4.1 Loi des probabilités totales
P(INDEX2) = P(INDEX2|SYNC) × P(SYNC) + P(INDEX2|DESYNC) × P(DESYNC)

### 4.2 Théorème de Bayes
P(INDEX1|INDEX2) = P(INDEX2|INDEX1) × P(INDEX1) / P(INDEX2)

### 4.3 Indépendance statistique
INDEX1 et INDEX2 sont indépendants si et seulement si :
P(INDEX2|INDEX1) = P(INDEX2) pour toutes les valeurs

## 5. IMPLÉMENTATION ALGORITHMIQUE

### 5.1 Algorithme de comptage
```
Pour chaque combinaison (i1, i2, i3) dans les données :
    count_i1_i2_i3[i1][i2][i3] += 1
    count_i1_i2[i1][i2] += 1
    count_i1[i1] += 1

Pour chaque i1, i2 :
    P(i2|i1) = count_i1_i2[i1][i2] / count_i1[i1]

Pour chaque i1, i2, i3 :
    P(i3|i1,i2) = count_i1_i2_i3[i1][i2][i3] / count_i1_i2[i1][i2]
```

### 5.2 Algorithme argmax
```
Pour chaque i1 :
    max_prob = 0
    most_likely = None
    Pour chaque i2 :
        Si P(i2|i1) > max_prob :
            max_prob = P(i2|i1)
            most_likely = i2
    INDEX2_most_likely[i1] = most_likely
```

## 6. MÉTRIQUES DE VALIDATION

### 6.1 Test d'indépendance Chi²
H0 : INDEX1 et INDEX2 sont indépendants
H1 : INDEX1 et INDEX2 sont dépendants

χ² = Σ[(Observé - Attendu)²/Attendu]

### 6.2 Information mutuelle
I(INDEX1; INDEX2) = Σ P(i1,i2) × log₂[P(i1,i2)/(P(i1)×P(i2))]

### 6.3 V de Cramér
V = √[χ²/(n × min(r-1, c-1))]

Ces formules constituent la base mathématique exacte pour l'analyse 
des probabilités conditionnelles dans le système Lupasco.
