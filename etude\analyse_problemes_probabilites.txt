ANALYSE APPROFONDIE DES PROBLÈMES - PROBABILITÉS CONDITIONNELLES
================================================================

Date: 2025-06-18
Analyste: Assistant IA
Source: methodes_probabilites_extraites.txt

## RÉSUMÉ EXÉCUTIF

Après extraction et analyse de 13 méthodes contenant les probabilités conditionnelles,
j'ai identifié LE PROBLÈME FONDAMENTAL qui cause le biais "toujours BANKER".

## MÉTHODES CRITIQUES IDENTIFIÉES

### 1. MÉTHODES CLOISONNÉES (Utilisées actuellement)
- _entrainer_modele_index2() - Ligne 626
- _entrainer_modele_index3() - Ligne 668  
- _predire_main_cloisonnee() - Ligne 844

### 2. MÉTHODES NON-CLOISONNÉES (Anciennes)
- _predire_index2_given_index1_synchrone() - Ligne 1279
- _predire_index3_given_index1_index2() - Ligne 1398

## DIAGNOSTIC TECHNIQUE PRÉCIS

### PROBLÈME 1: LOGIQUE DE PRÉ-CALCUL STATIQUE

**MÉTHODE _entrainer_modele_index3() - LIGNES 222-229:**
```python
# Identifier INDEX3_most_likely
if contingence_index3[i1][i2]:
    probas_i3 = probabilites_index3[i1][i2]
    most_likely = max(probas_i3.items(), key=lambda x: x[1])  # ← PROBLÈME ICI
    predictions_index3[i1][i2] = {
        'most_likely': most_likely[0],  # ← STOCKAGE STATIQUE
        'probabilite': most_likely[1],
        'toutes_probas': probas_i3
    }
```

**ANALYSE:**
- Cette méthode PRÉ-CALCULE les prédictions pour TOUTES les combinaisons (INDEX1, INDEX2)
- Elle STOCKE la classe majoritaire (BANKER) comme prédiction fixe
- Résultat: Table statique où tout pointe vers BANKER

### PROBLÈME 2: CONSULTATION AU LIEU DE CALCUL

**MÉTHODE _predire_main_cloisonnee() - LIGNES 262-278:**
```python
# Étape 1 : Prédire INDEX2 avec seulement INDEX1
if index1_actuel in modele_index2['predictions']:
    index2_prediction = modele_index2['predictions'][index1_actuel]  # ← CONSULTATION
    index2_predit = index2_prediction['most_likely']  # ← VALEUR PRÉ-CALCULÉE

    # Étape 2 : Prédire INDEX3 avec INDEX1 + INDEX2 prédit
    if (index1_actuel in modele_index3['predictions'] and
        index2_predit in modele_index3['predictions'][index1_actuel]):

        index3_prediction = modele_index3['predictions'][index1_actuel][index2_predit]  # ← CONSULTATION
        index3_predit = index3_prediction['most_likely']  # ← VALEUR PRÉ-CALCULÉE
```

**ANALYSE:**
- Cette méthode ne CALCULE PAS de prédiction
- Elle CONSULTE simplement les valeurs pré-calculées
- C'est un simple lookup dans une table statique

### PROBLÈME 3: MÊME ERREUR DANS TOUTES LES MÉTHODES

**PATTERN RÉCURRENT IDENTIFIÉ:**
```python
# Dans TOUTES les méthodes de prédiction:
most_likely = max(probas.items(), key=lambda x: x[1])
predictions[combinaison] = {'most_likely': most_likely[0]}
```

**MÉTHODES AFFECTÉES:**
1. _entrainer_modele_index2() - Ligne 157
2. _entrainer_modele_index3() - Ligne 224
3. _predire_index2_given_index1_synchrone() - Ligne 363
4. _predire_index3_given_index1_index2() - Ligne 476

## CAUSE RACINE CONFIRMÉE

### ARCHITECTURE INCORRECTE:
```
ACTUEL (INCORRECT):
Entraînement → Pré-calcul argmax() → Stockage statique → Consultation

CORRECT:
Entraînement → Stockage probabilités → Calcul dynamique → Prédiction contextuelle
```

### CONSÉQUENCE:
- BANKER étant majoritaire (~50.6%) dans TOUTES les combinaisons
- argmax() sélectionne BANKER pour TOUTES les combinaisons
- Table de prédictions: 100% BANKER
- Consultation: Toujours BANKER

## SOLUTION TECHNIQUE REQUISE

### ARCHITECTURE CORRECTE:
```python
def predire_main_contextuelle(self, index1_actuel, historique_complet):
    """
    Prédiction contextuelle dynamique
    """
    # 1. Filtrer historique pertinent
    contexte_similaire = self._filtrer_contexte(historique_complet, index1_actuel)
    
    # 2. Calculer probabilités contextuelles
    probas_index2 = self._calculer_probas_contextuelles(contexte_similaire, index1_actuel)
    
    # 3. Prédiction probabiliste (pas argmax)
    index2_predit = self._predire_probabiliste(probas_index2)
    
    # 4. Même logique pour INDEX3
    probas_index3 = self._calculer_probas_contextuelles(contexte_similaire, index1_actuel, index2_predit)
    index3_predit = self._predire_probabiliste(probas_index3)
    
    return index2_predit, index3_predit

def _predire_probabiliste(self, probabilites):
    """
    Prédiction probabiliste au lieu d'argmax déterministe
    """
    if max(probabilites.values()) - min(probabilites.values()) < 0.1:  # Différence < 10%
        # Échantillonnage probabiliste
        classes = list(probabilites.keys())
        weights = list(probabilites.values())
        return np.random.choice(classes, p=weights)
    else:
        # argmax seulement si différence significative
        return max(probabilites.items(), key=lambda x: x[1])[0]
```

## CONCLUSION

Le problème n'est PAS dans argmax() en soi, mais dans l'ARCHITECTURE de pré-calcul statique qui:

1. **Pré-calcule** toutes les prédictions avec argmax()
2. **Stocke** les classes majoritaires comme prédictions fixes
3. **Consulte** ces valeurs statiques au lieu de calculer dynamiquement

**SOLUTION:** Remplacer l'architecture de pré-calcul par un système de prédiction contextuelle dynamique qui calcule les probabilités à la demande selon le contexte spécifique de chaque partie.
