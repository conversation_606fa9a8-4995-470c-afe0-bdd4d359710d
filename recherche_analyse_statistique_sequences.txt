RECHERCHE SUR L'ANALYSE STATISTIQUE DES SÉQUENCES CONSÉCUTIVES
================================================================

Date: 2025-06-17
Contexte: Analyse des séquences consécutives dans le dataset Baccarat Lupasco (100 000 parties)
Objectif: Analyser les runs/séquences de SYNC/DESYNC, pair_4/pair_6/impair_5, PLAYER/BANKER

## 1. TESTS STATISTIQUES POUR LES SÉQUENCES CONSÉCUTIVES

### 1.1 Runs Test (Test des Séries)
- **Définition**: Test statistique pour détecter la non-randomness dans une séquence de données
- **Application**: Détermine si une séquence de données binaires (ex: SYNC/DESYNC) est aléatoire
- **Principe**: Compte le nombre de "runs" (séquences consécutives d'éléments identiques)
- **Référence NIST**: SP 800-22 - Suite de tests statistiques pour l'aléatoire
- **Utilité**: Idéal pour tester l'indépendance des événements consécutifs

### 1.2 Wald-Wolfowitz Run Test
- **Usage**: Version spécifique du runs test pour données non-paramétriques
- **Avantage**: Détecte les patterns non-aléatoires dans les séquences
- **Application**: Parfait pour analyser les streaks de PLAYER/BANKER

## 2. ANALYSE DES DISTRIBUTIONS DE LONGUEURS DE RUNS

### 2.1 Distribution Géométrique
- **Théorie**: Pour des événements indépendants avec probabilité p, la longueur des runs suit une distribution géométrique
- **Formule**: P(X = k) = (1-p)^(k-1) * p
- **Application**: Comparer les longueurs observées vs théoriques pour SYNC/DESYNC

### 2.2 Distribution de Fibonacci (cas spéciaux)
- **Contexte**: Pour certaines contraintes de séquences
- **Application**: Analyse des patterns complexes dans les données Lupasco

## 3. TESTS DE RANDOMNESS ET INDÉPENDANCE

### 3.1 Chi-Square Goodness of Fit Test
- **Usage**: Tester si les fréquences observées correspondent aux fréquences attendues
- **Application**: Vérifier si les distributions de longueurs de runs suivent les modèles théoriques
- **Formule**: χ² = Σ[(Observé - Attendu)²/Attendu]

### 3.2 Test de Kolmogorov-Smirnov
- **Usage**: Comparer la distribution empirique avec une distribution théorique
- **Avantage**: Plus puissant que le chi-square pour les distributions continues

## 4. MÉTHODES D'ANALYSE SPÉCIFIQUES AU GAMBLING

### 4.1 Hot Hand vs Gambler's Fallacy
- **Hot Hand**: Tendance à croire que les succès consécutifs continuent
- **Gambler's Fallacy**: Croyance que les événements passés affectent les futurs
- **Analyse**: Mesurer les corrélations sérielles dans les séquences

### 4.2 Streak Analysis dans le Baccarat
- **Recherche**: Études montrent que les joueurs suivent les trends plutôt que l'indépendance
- **Métrique**: Analyser les longueurs moyennes, maximales et distributions des streaks

## 5. MÉTRIQUES STATISTIQUES CLÉS

### 5.1 Longueur Moyenne des Runs
- **Formule théorique**: Pour probabilité p, longueur moyenne = 1/p
- **Comparaison**: Mesurer écart entre observé et théorique

### 5.2 Longueur Maximale des Runs
- **Distribution**: Suit approximativement une distribution de Gumbel
- **Significance**: Tester si les runs maximaux sont statistiquement significatifs

### 5.3 Nombre Total de Runs
- **Théorie**: Pour n observations avec probabilité p, nombre attendu de runs ≈ 2np(1-p) + 1
- **Test**: Comparer avec les observations

## 6. TESTS AVANCÉS POUR PATTERNS COMPLEXES

### 6.1 Autocorrélation Analysis
- **Usage**: Détecter les dépendances temporelles dans les séquences
- **Formule**: r(k) = Cov(X_t, X_{t+k}) / Var(X_t)

### 6.2 Spectral Analysis
- **Application**: Identifier les périodicités cachées dans les données
- **Méthode**: Transformée de Fourier des séquences

### 6.3 Entropy Analysis
- **Shannon Entropy**: Mesurer l'information contenue dans les séquences
- **Conditional Entropy**: Analyser la prédictibilité basée sur l'historique

## 7. OUTILS ET IMPLÉMENTATIONS

### 7.1 Tests Disponibles en Python
- scipy.stats.runs_test()
- statsmodels pour autocorrélation
- numpy pour distributions théoriques

### 7.2 Tests Disponibles en R
- randtests package pour runs test
- tseries pour analyse temporelle

## 8. CONSIDÉRATIONS SPÉCIALES POUR LUPASCO

### 8.1 Système à 3 États (INDEX3)
- **Défi**: PLAYER/BANKER/TIE avec probabilités inégales
- **Solution**: Adapter les tests pour distributions non-uniformes

### 8.2 Interactions Multi-Index
- **Complexité**: Analyser les corrélations entre INDEX1, INDEX2, INDEX3
- **Méthode**: Tests de contingence multivariés

### 8.3 Hasard Cryptographique
- **Avantage**: Données générées avec CSPRNG de haute qualité
- **Attente**: Résultats proches des modèles théoriques d'indépendance

## 9. PLAN D'ANALYSE RECOMMANDÉ

### Phase 1: Tests de Base
1. Runs test pour chaque type de séquence
2. Distribution des longueurs de runs
3. Chi-square goodness of fit

### Phase 2: Analyse Avancée
1. Autocorrélation analysis
2. Tests de stationnarité
3. Analyse des patterns multi-index

### Phase 3: Validation
1. Comparaison avec modèles théoriques
2. Tests de robustesse
3. Analyse des outliers

## 10. RÉFÉRENCES ACADÉMIQUES

- NIST SP 800-22: Statistical Test Suite for Random and Pseudorandom Number Generators
- "Baccarat gamblers follow trends rather than adhere to the gambler's fallacy" (ScienceDirect)
- "An Application of the Runs Test to Test for Randomness" (PMC)
- "The Theory of Gambling and Statistical Logic" (Richard Epstein)

## 11. MÉTRIQUES DE SORTIE ATTENDUES

Pour chaque type de séquence (SYNC/DESYNC, pair_4/pair_6/impair_5, PLAYER/BANKER):
- Nombre total de runs
- Longueur moyenne des runs
- Longueur maximale observée
- Distribution des longueurs
- P-value du runs test
- Écart par rapport aux modèles théoriques
- Coefficient d'autocorrélation
- Entropie de Shannon

Cette recherche fournit la base théorique solide pour analyser statistiquement 
les séquences consécutives dans le dataset Baccarat Lupasco de 100 000 parties.
