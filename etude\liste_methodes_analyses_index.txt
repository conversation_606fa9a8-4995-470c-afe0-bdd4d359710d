LISTE DES MÉTHODES D'ANALYSE PAR INDEX - SYSTÈME LUPASCO
========================================================

Date: 2025-06-18
Source: analyseur_sequences_lupasco.py

## MÉTHODES D'ANALYSE INDEX1 (SYNC/DESYNC)

### 1. MÉTHODES PRINCIPALES
- `analyser_runs()` - Ligne 295
  * Analyse des runs/séquences pour INDEX1
  * Calcul longueurs moyennes, distributions, autocorrélation

- `_valider_regle_lupasco()` - Ligne 1079
  * Validation P(INDEX1(t+1)|INDEX2(t))
  * Précision règle Lupasco temporelle
  * Tests statistiques Chi², V de Cramér

- `_compter_transitions_temporelles()` - Ligne 1135
  * Comptage transitions INDEX2(t) → INDEX1(t+1)
  * Algorithme temporel selon formules optimales

- `_calculer_precision_lupasco()` - <PERSON>gne 1151
  * Validation règle Lupasco : pair_4/pair_6 perpétuent, impair_5 alterne
  * Calcul précision sur transitions temporelles

- `_information_mutuelle_temporelle()` - Ligne 1240
  * Information mutuelle I(INDEX2(t); INDEX1(t+1))
  * Mesure dépendance temporelle selon Harvard

### 2. MÉTHODES D'INFLUENCE CAUSALE
- `_analyser_influences_causales()` - Ligne 1727
  * Influence INDEX1 → INDEX2 (directe)
  * Influence INDEX1 → INDEX3 (directe)
  * Écarts par rapport à l'indépendance

### 3. MÉTHODES DE PRÉDICTION
- `_analyser_capacites_predictives()` - Ligne 1842
  * Capacité prédictive INDEX1 → INDEX2
  * Précision prédictions basées sur INDEX1

## MÉTHODES D'ANALYSE INDEX2 (pair_4/pair_6/impair_5)

### 1. MÉTHODES PRINCIPALES
- `analyser_runs()` - Ligne 295
  * Analyse des runs/séquences pour INDEX2
  * Distributions, longueurs moyennes, patterns

- `_analyser_index2_given_index1()` - Ligne 1619
  * Analyse P(INDEX2|INDEX1)
  * Probabilités conditionnelles synchrones
  * Tests Chi² d'indépendance

- `_predire_index2_given_index1_synchrone()` - Ligne 1279
  * Calcul P(INDEX2(t+1)|INDEX1(t+1))
  * Identification INDEX2_most_likely
  * Tests statistiques synchrones

### 2. MÉTHODES D'ENTRAÎNEMENT
- `_entrainer_modele_index2()` - Ligne 626
  * Entraînement P(INDEX2|INDEX1) sur historique
  * Construction table contingence
  * Calcul probabilités et prédictions

### 3. MÉTHODES DE VALIDATION
- `_verifier_contraintes_synchrones()` - Ligne 1384
  * Vérifications contraintes P(INDEX2|INDEX1)
  * Validation sommes probabilités = 1

- `_tests_statistiques_synchrones()` - Ligne 1353
  * Tests Chi² et V de Cramér synchrones
  * Validation indépendance INDEX1-INDEX2

### 4. MÉTHODES D'INFLUENCE
- `_analyser_influences_causales()` - Ligne 1727
  * Influence INDEX2 → INDEX3
  * Influence INDEX1 → INDEX2
  * Information mutuelle INDEX1-INDEX2

## MÉTHODES D'ANALYSE INDEX3 (PLAYER/BANKER/TIE)

### 1. MÉTHODES PRINCIPALES
- `analyser_runs()` - Ligne 295
  * Analyse des runs/séquences pour INDEX3
  * Patterns PLAYER/BANKER, distributions

- `_analyser_index3_given_index1_index2()` - Ligne 1676
  * Analyse P(INDEX3|INDEX1, INDEX2)
  * Probabilités conditionnelles 3D
  * Tests Chi² par combinaison

- `_predire_index3_given_index1_index2()` - Ligne 1398
  * Calcul P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1))
  * Identification INDEX3_most_likely
  * Métriques performance Paris-Saclay

### 2. MÉTHODES D'ENTRAÎNEMENT
- `_entrainer_modele_index3()` - Ligne 668
  * Entraînement P(INDEX3|INDEX1, INDEX2) sur historique
  * Construction contingence 3D
  * Calcul probabilités et prédictions

### 3. MÉTHODES DE VALIDATION ET MÉTRIQUES
- `_calculer_metriques_performance()` - Ligne 1483
  * Précision globale, F1-Score, métriques par classe
  * True Positive, False Positive, False Negative

- `_calculer_matrice_confusion()` - Ligne 1541
  * Matrice de confusion INDEX3
  * Comparaison prédictions vs réalité

- `_calculer_v_cramer_performance()` - Ligne 1555
  * V de Cramér performance selon Real Statistics
  * Mesure association prédictions-réalité

- `_verifier_contraintes_finales()` - Ligne 1604
  * Vérifications contraintes P(INDEX3|INDEX1,INDEX2)
  * Validation sommes probabilités = 1

### 4. MÉTHODES DE PRÉDICTION CLOISONNÉE
- `_predire_main_cloisonnee()` - Ligne 844
  * Prédiction INDEX3 avec accès seulement INDEX1
  * Prédiction en 2 étapes : INDEX1 → INDEX2 → INDEX3

- `predire_main_suivante_cloisonnee()` - Ligne 869
  * Interface prédiction cloisonnée complète
  * Utilise modèles entraînés avec cloisonnement

- `_tester_predictions_cloisonnees()` - Ligne 768
  * Test prédictions avec cloisonnement strict
  * Validation temporelle 80/20
  * Métriques précision cloisonnée

### 5. MÉTHODES D'INFLUENCE
- `_analyser_influences_causales()` - Ligne 1727
  * Influence INDEX2 → INDEX3
  * Influence INDEX1 → INDEX3 (directe)
  * Information mutuelle INDEX2-INDEX3

- `_analyser_capacites_predictives()` - Ligne 1842
  * Capacité prédictive (INDEX1, INDEX2) → INDEX3
  * Précision prédictions combinées

## MÉTHODES TRANSVERSALES (TOUS INDEX)

### 1. MÉTHODES D'EXTRACTION
- `_charger_avec_streaming()` - Ligne 108
  * Extraction INDEX1, INDEX2, INDEX3 depuis JSON
  * Mode streaming pour gros fichiers

- `_extraire_sequences()` - Ligne 216
  * Extraction séquences individuelles et combinées
  * Alignement données INDEX1, INDEX2, INDEX3

### 2. MÉTHODES D'ANALYSE GLOBALE
- `analyser_probabilites_conditionnelles()` - Ligne 510
  * Analyse complète des 3 INDEX
  * Probabilités conditionnelles, influences causales

- `analyser_predictions_lupasco()` - Ligne 556
  * Analyse prédictive cloisonnée complète
  * Validation règle Lupasco + prédictions

### 3. MÉTHODES D'ENTRAÎNEMENT GLOBAL
- `_entrainer_modeles_cloisonnes()` - Ligne 596
  * Entraînement modèles INDEX2 et INDEX3
  * Cloisonnement temporel strict

- `_valider_predictions_cloisonnees()` - Ligne 719
  * Validation temporelle avec split 80/20
  * Tests prédictions cloisonnées

### 4. MÉTHODES DE RAPPORT
- `generer_rapport()` - Ligne 1996
  * Rapport complet analyses INDEX1, INDEX2, INDEX3
  * Métriques, probabilités, prédictions

- `generer_rapport_predictions_optimales()` - Ligne 919
  * Rapport spécialisé prédictions cloisonnées
  * Matrices confusion, exemples prédictions

## RÉSUMÉ PAR CATÉGORIE

### ANALYSE DESCRIPTIVE
- INDEX1: 5 méthodes principales
- INDEX2: 8 méthodes principales  
- INDEX3: 12 méthodes principales

### PRÉDICTION
- INDEX1: 3 méthodes (validation règle Lupasco)
- INDEX2: 4 méthodes (entraînement + prédiction)
- INDEX3: 6 méthodes (entraînement + prédiction + validation)

### VALIDATION
- INDEX1: 4 méthodes (tests statistiques)
- INDEX2: 3 méthodes (contraintes + tests)
- INDEX3: 4 méthodes (métriques + matrices + contraintes)

**TOTAL: 49 méthodes spécialisées pour l'analyse des INDEX Lupasco**
