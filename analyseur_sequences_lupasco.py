#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ANALYSEUR STATISTIQUE DES SÉQUENCES CONSÉCUTIVES - SYSTÈME LUPASCO
===================================================================

Programme d'analyse statistique des séquences consécutives basé sur la recherche
académique pour le dataset Baccarat Lupasco de 100 000 parties.

Analyse les runs/séquences de :
- SYNC/DESYNC (INDEX1)
- pair_4/pair_6/impair_5 (INDEX2)  
- PLAYER/BANKER (INDEX3)

Basé sur : recherche_analyse_statistique_sequences.txt
"""

import json
import numpy as np
from scipy import stats
from scipy.stats import chi2, kstest, geom, fisher_exact, contingency
from collections import Counter, defaultdict
from itertools import combinations
from datetime import datetime
import warnings
import gc
import os
warnings.filterwarnings('ignore')

# Imports optionnels
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False

# Parser JSON streaming pour gros fichiers
try:
    import ijson
    HAS_IJSON = True
except ImportError:
    HAS_IJSON = False


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences consécutives pour le système Lupasco
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec le fichier JSON
        
        Args:
            fichier_json: Chemin vers le fichier JSON du dataset Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        self.nb_parties_total = 0
        
        print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
        print("=" * 60)
        print(f"📂 Fichier à analyser : {fichier_json}")
        
    def charger_donnees(self):
        """Charge et extrait les données du fichier JSON volumineux (7GB) avec streaming"""
        print("\n📊 Chargement des données...")

        try:
            # Obtenir la taille du fichier
            taille_fichier = os.path.getsize(self.fichier_json)
            taille_gb = taille_fichier / (1024 * 1024 * 1024)
            taille_mb = taille_fichier / (1024 * 1024)

            if taille_gb >= 1:
                print(f"📁 Taille du fichier : {taille_gb:.1f} GB")
                print("🚨 FICHIER TRÈS VOLUMINEUX DÉTECTÉ !")

                if HAS_IJSON:
                    print("⚡ Utilisation du mode streaming ijson pour éviter les problèmes de mémoire...")
                    self._charger_avec_streaming()
                else:
                    print("❌ Module ijson requis pour les fichiers > 1 GB")
                    print("💡 Installation : pip install ijson")
                    raise ImportError("ijson requis pour les gros fichiers")
            else:
                print(f"📁 Taille du fichier : {taille_mb:.1f} MB")
                print("🔄 Chargement standard...")
                self._charger_standard()

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise

    def _charger_avec_streaming(self):
        """Charge le fichier JSON avec streaming ijson (pour fichiers 7GB)"""
        print("🔄 Lecture streaming du fichier JSON...")

        # Initialiser les listes de séquences directement
        index1_seq = []
        index2_seq = []
        index3_seq = []
        index1_index2_seq = []
        index1_index3_seq = []
        index2_index3_seq = []
        index1_index2_index3_seq = []

        total_parties = 0
        total_mains = 0

        # Nettoyage mémoire préalable
        gc.collect()

        try:
            with open(self.fichier_json, 'rb') as f:
                print("   📖 Parsing streaming des parties...")

                # Parser streaming des parties
                parties = ijson.items(f, 'parties.item')

                for partie in parties:
                    total_parties += 1

                    # Traiter chaque main de la partie
                    for main in partie['mains']:
                        idx1 = main['index1_sync_state']
                        idx2 = main['index2_cards_category']
                        idx3 = main['index3_result']

                        # INDEX individuels
                        index1_seq.append(idx1)
                        index2_seq.append(idx2)

                        # INDEX3 : PLAYER/BANKER (exclure TIE)
                        if idx3 in ['PLAYER', 'BANKER']:
                            index3_seq.append(idx3)

                            # Combinaisons avec INDEX3
                            index1_index3_seq.append(f"{idx1}_{idx3}")
                            index2_index3_seq.append(f"{idx2}_{idx3}")
                            index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                        # Combinaisons sans INDEX3
                        index1_index2_seq.append(f"{idx1}_{idx2}")

                        total_mains += 1

                    # Affichage du progrès tous les 5000 parties
                    if total_parties % 5000 == 0:
                        print(f"   📈 Parties traitées : {total_parties:,} - {total_mains:,} mains extraites")
                        gc.collect()  # Nettoyage mémoire périodique

            # Stocker les séquences
            self.sequences = {
                'INDEX1': index1_seq,
                'INDEX2': index2_seq,
                'INDEX3': index3_seq,
                'INDEX1_INDEX2': index1_index2_seq,
                'INDEX1_INDEX3': index1_index3_seq,
                'INDEX2_INDEX3': index2_index3_seq,
                'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
            }

            print(f"✅ Streaming terminé : {total_parties:,} parties, {total_mains:,} mains")
            print(f"   - INDEX1 : {len(index1_seq):,} valeurs")
            print(f"   - INDEX2 : {len(index2_seq):,} valeurs")
            print(f"   - INDEX3 : {len(index3_seq):,} valeurs")
            print(f"   - Combinaisons : {len(index1_index2_seq):,} INDEX1_INDEX2")

            # Stocker le nombre de parties pour le rapport
            self.nb_parties_total = total_parties

            # Nettoyage final
            gc.collect()

        except Exception as e:
            print(f"❌ Erreur streaming : {e}")
            raise

    def _charger_standard(self):
        """Chargement standard pour fichiers plus petits"""
        gc.collect()

        with open(self.fichier_json, 'r', encoding='utf-8') as f:
            print("   📖 Parsing JSON standard...")
            self.donnees = json.load(f)

        nb_parties = len(self.donnees['parties'])
        print(f"✅ {nb_parties:,} parties chargées")

        # Stocker le nombre de parties pour le rapport
        self.nb_parties_total = nb_parties

        # Extraire les séquences
        self._extraire_sequences()

        # Libérer la mémoire du JSON original
        del self.donnees
        gc.collect()
    
    def _extraire_sequences(self):
        """Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties"""
        print("🔍 Extraction des séquences et combinaisons...")

        # Initialiser les listes de séquences individuelles
        index1_seq = []  # SYNC/DESYNC
        index2_seq = []  # pair_4/pair_6/impair_5
        index3_seq = []  # PLAYER/BANKER (exclure TIE pour analyse binaire)

        # Initialiser les listes de séquences combinées
        index1_index2_seq = []  # SYNC/DESYNC + pair_4/pair_6/impair_5
        index1_index3_seq = []  # SYNC/DESYNC + PLAYER/BANKER
        index2_index3_seq = []  # pair_4/pair_6/impair_5 + PLAYER/BANKER
        index1_index2_index3_seq = []  # Combinaison des 3 INDEX

        total_mains = 0
        nb_parties = len(self.donnees['parties'])

        # Définir l'intervalle d'affichage selon le nombre de parties
        if nb_parties <= 1000:
            intervalle = 100
        elif nb_parties <= 10000:
            intervalle = 1000
        else:
            intervalle = 10000  # Pour 100 000 parties, affichage tous les 10 000

        print(f"📊 Traitement de {nb_parties:,} parties (affichage tous les {intervalle:,})...")

        for i, partie in enumerate(self.donnees['parties'], 1):
            for main in partie['mains']:
                # INDEX individuels
                idx1 = main['index1_sync_state']
                idx2 = main['index2_cards_category']
                idx3 = main['index3_result']

                index1_seq.append(idx1)
                index2_seq.append(idx2)

                # INDEX3 : PLAYER/BANKER (exclure TIE pour runs binaires)
                if idx3 in ['PLAYER', 'BANKER']:
                    index3_seq.append(idx3)

                    # Combinaisons avec INDEX3 (seulement si pas TIE)
                    index1_index3_seq.append(f"{idx1}_{idx3}")
                    index2_index3_seq.append(f"{idx2}_{idx3}")
                    index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                # Combinaisons sans INDEX3 (toujours possibles)
                index1_index2_seq.append(f"{idx1}_{idx2}")

                total_mains += 1

            # Affichage cyclique du progrès
            if i % intervalle == 0 or i == nb_parties:
                pourcentage = (i / nb_parties) * 100
                print(f"   📈 Parties traitées : {i:,}/{nb_parties:,} ({pourcentage:.1f}%) - {total_mains:,} mains extraites")

        self.sequences = {
            # INDEX individuels
            'INDEX1': index1_seq,
            'INDEX2': index2_seq,
            'INDEX3': index3_seq,

            # Combinaisons d'INDEX
            'INDEX1_INDEX2': index1_index2_seq,
            'INDEX1_INDEX3': index1_index3_seq,
            'INDEX2_INDEX3': index2_index3_seq,
            'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
        }

        print(f"✅ {total_mains:,} mains extraites")
        print(f"   - INDEX1 (SYNC/DESYNC) : {len(index1_seq):,} valeurs")
        print(f"   - INDEX2 (pair_4/6/impair_5) : {len(index2_seq):,} valeurs")
        print(f"   - INDEX3 (PLAYER/BANKER) : {len(index3_seq):,} valeurs")
        print(f"   - INDEX1_INDEX2 : {len(index1_index2_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX3 : {len(index1_index3_seq):,} combinaisons")
        print(f"   - INDEX2_INDEX3 : {len(index2_index3_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX2_INDEX3 : {len(index1_index2_index3_seq):,} combinaisons")
    
    def analyser_runs(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse les runs (séquences consécutives) d'une séquence
        
        Args:
            sequence: Liste des valeurs de la séquence
            nom_sequence: Nom de la séquence pour l'affichage
            
        Returns:
            dict: Résultats de l'analyse des runs
        """
        print(f"\n🔬 Analyse des runs : {nom_sequence}")
        
        # Identifier les runs
        runs = []
        if not sequence:
            return {}
        
        current_value = sequence[0]
        current_length = 1
        
        for i in range(1, len(sequence)):
            if sequence[i] == current_value:
                current_length += 1
            else:
                runs.append((current_value, current_length))
                current_value = sequence[i]
                current_length = 1
        
        # Ajouter le dernier run
        runs.append((current_value, current_length))
        
        # Analyser par type de valeur
        resultats = {}
        valeurs_uniques = list(set(sequence))
        
        for valeur in valeurs_uniques:
            longueurs_runs = [length for value, length in runs if value == valeur]
            
            if longueurs_runs:
                resultats[valeur] = self._analyser_runs_valeur(
                    longueurs_runs, valeur, len(sequence), sequence.count(valeur)
                )
        
        # Statistiques globales
        resultats['global'] = self._analyser_runs_global(runs, sequence)
        
        return resultats
    
    def _analyser_runs_valeur(self, longueurs: list, valeur: str, n_total: int, n_valeur: int) -> dict:
        """Analyse les runs pour une valeur spécifique"""
        
        # Statistiques descriptives
        stats_desc = {
            'nombre_runs': len(longueurs),
            'longueur_moyenne': np.mean(longueurs),
            'longueur_mediane': np.median(longueurs),
            'longueur_max': max(longueurs),
            'longueur_min': min(longueurs),
            'ecart_type': np.std(longueurs),
            'distribution': Counter(longueurs)
        }
        
        # Probabilité théorique
        p = n_valeur / n_total
        
        # Longueur moyenne théorique (distribution géométrique)
        longueur_moyenne_theorique = 1 / p if p > 0 else float('inf')
        
        # Nombre de runs théorique
        # Formule : E[R] ≈ 2np(1-p) + 1 pour le nombre total de runs
        # Pour une valeur spécifique : approximativement n_valeur / longueur_moyenne_theorique
        nombre_runs_theorique = n_valeur / longueur_moyenne_theorique if longueur_moyenne_theorique != float('inf') else 0
        
        # Tests statistiques
        tests = {}
        
        # Test de Kolmogorov-Smirnov contre distribution géométrique
        if p > 0 and p < 1:
            # Distribution géométrique théorique
            longueurs_max = max(longueurs)
            x_theorique = np.arange(1, longueurs_max + 1)
            cdf_theorique = geom.cdf(x_theorique, p)
            
            # CDF empirique
            longueurs_sorted = np.sort(longueurs)
            cdf_empirique = np.arange(1, len(longueurs) + 1) / len(longueurs)
            
            # Test KS (approximation)
            try:
                ks_stat, ks_pvalue = kstest(longueurs, lambda x: geom.cdf(x, p))
                tests['ks_test'] = {'statistic': ks_stat, 'p_value': ks_pvalue}
            except:
                tests['ks_test'] = {'statistic': None, 'p_value': None}
        
        return {
            'statistiques': stats_desc,
            'theorique': {
                'probabilite': p,
                'longueur_moyenne_theorique': longueur_moyenne_theorique,
                'nombre_runs_theorique': nombre_runs_theorique
            },
            'tests': tests,
            'ecarts': {
                'ecart_longueur_moyenne': stats_desc['longueur_moyenne'] - longueur_moyenne_theorique,
                'ecart_nombre_runs': stats_desc['nombre_runs'] - nombre_runs_theorique
            }
        }
    
    def _analyser_runs_global(self, runs: list, sequence: list) -> dict:
        """Analyse globale des runs"""
        
        # Nombre total de runs
        nombre_total_runs = len(runs)
        
        # Longueurs de tous les runs
        toutes_longueurs = [length for _, length in runs]
        
        # Statistiques globales
        stats_globales = {
            'nombre_total_runs': nombre_total_runs,
            'longueur_moyenne_globale': np.mean(toutes_longueurs),
            'longueur_max_globale': max(toutes_longueurs),
            'distribution_globale': Counter(toutes_longueurs)
        }
        
        # Runs Test (test de randomness)
        # Implémentation simplifiée du runs test
        n = len(sequence)
        valeurs_uniques = list(set(sequence))
        
        if len(valeurs_uniques) == 2:
            # Runs test pour séquence binaire
            n1 = sequence.count(valeurs_uniques[0])
            n2 = sequence.count(valeurs_uniques[1])
            
            # Nombre de runs observé
            R = nombre_total_runs
            
            # Moyenne et variance théoriques
            mu_R = (2 * n1 * n2) / (n1 + n2) + 1
            sigma2_R = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2)**2 * (n1 + n2 - 1))
            sigma_R = np.sqrt(sigma2_R)
            
            # Z-score
            if sigma_R > 0:
                z_score = (R - mu_R) / sigma_R
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            else:
                z_score = 0
                p_value = 1
            
            stats_globales['runs_test'] = {
                'runs_observes': R,
                'runs_attendus': mu_R,
                'z_score': z_score,
                'p_value': p_value,
                'significatif': p_value < 0.05
            }
        
        return stats_globales
    
    def calculer_autocorrelation(self, sequence: list, max_lag: int = 20) -> dict:
        """
        Calcule l'autocorrélation de la séquence
        
        Args:
            sequence: Séquence à analyser
            max_lag: Nombre maximum de lags à calculer
            
        Returns:
            dict: Coefficients d'autocorrélation
        """
        # Convertir en valeurs numériques pour l'autocorrélation
        valeurs_uniques = list(set(sequence))
        mapping = {val: i for i, val in enumerate(valeurs_uniques)}
        sequence_num = [mapping[val] for val in sequence]
        
        autocorr = {}
        n = len(sequence_num)
        
        for lag in range(1, min(max_lag + 1, n // 4)):
            # Calcul de l'autocorrélation pour le lag donné
            x1 = sequence_num[:-lag]
            x2 = sequence_num[lag:]
            
            if len(x1) > 0 and len(x2) > 0:
                corr = np.corrcoef(x1, x2)[0, 1]
                autocorr[lag] = corr if not np.isnan(corr) else 0
        
        return autocorr
    
    def calculer_entropie_shannon(self, sequence: list) -> float:
        """
        Calcule l'entropie de Shannon de la séquence
        
        Args:
            sequence: Séquence à analyser
            
        Returns:
            float: Entropie de Shannon
        """
        # Compter les fréquences
        compteur = Counter(sequence)
        n = len(sequence)
        
        # Calculer l'entropie
        entropie = 0
        for count in compteur.values():
            p = count / n
            if p > 0:
                entropie -= p * np.log2(p)
        
        return entropie

    def analyser_probabilites_conditionnelles(self):
        """
        Analyse complète des probabilités conditionnelles selon les spécifications Lupasco
        """
        print("\nANALYSE DES PROBABILITÉS CONDITIONNELLES")
        print("=" * 50)

        # Extraire les données individuelles (sans combinaisons pour cette analyse)
        index1_data = self.sequences['INDEX1']
        index2_data = self.sequences['INDEX2']
        index3_data = self.sequences['INDEX3']

        # Créer les données alignées (même longueur)
        min_length = min(len(index1_data), len(index2_data), len(index3_data))
        index1_aligned = index1_data[:min_length]
        index2_aligned = index2_data[:min_length]
        index3_aligned = index3_data[:min_length]

        resultats = {}

        # 1. Analyse INDEX2 en fonction de INDEX1
        print("\n1. ANALYSE INDEX2 en fonction de INDEX1")
        resultats['index2_given_index1'] = self._analyser_index2_given_index1(
            index1_aligned, index2_aligned
        )

        # 2. Analyse INDEX3 en fonction de (INDEX1, INDEX2)
        print("\n2. ANALYSE INDEX3 en fonction de (INDEX1, INDEX2)")
        resultats['index3_given_index1_index2'] = self._analyser_index3_given_index1_index2(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 3. Mesures d'influence causale
        print("\n3. MESURES D'INFLUENCE CAUSALE")
        resultats['influences_causales'] = self._analyser_influences_causales(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 4. Analyses prédictives
        print("\n4. ANALYSES PRÉDICTIVES")
        resultats['analyses_predictives'] = self._analyser_capacites_predictives(
            index1_aligned, index2_aligned, index3_aligned
        )

        return resultats

    def _analyser_index2_given_index1(self, index1_data, index2_data):
        """Analyse P(INDEX2|INDEX1)"""
        print("   Calcul des probabilités conditionnelles P(INDEX2|INDEX1)...")

        # Créer tableau de contingence
        from collections import defaultdict
        contingence = defaultdict(lambda: defaultdict(int))

        for i1, i2 in zip(index1_data, index2_data):
            contingence[i1][i2] += 1

        # Calculer probabilités conditionnelles
        probas_conditionnelles = {}
        totaux_index1 = {}

        # Calculer totaux pour chaque valeur d'INDEX1
        for i1 in contingence:
            totaux_index1[i1] = sum(contingence[i1].values())

        # Calculer P(INDEX2|INDEX1)
        for i1 in contingence:
            probas_conditionnelles[i1] = {}
            for i2 in contingence[i1]:
                probas_conditionnelles[i1][i2] = contingence[i1][i2] / totaux_index1[i1]

        # Créer matrice pour test chi2
        valeurs_i1 = sorted(contingence.keys())
        valeurs_i2 = sorted(set(i2 for i1_dict in contingence.values() for i2 in i1_dict.keys()))

        matrice_contingence = []
        for i1 in valeurs_i1:
            ligne = []
            for i2 in valeurs_i2:
                ligne.append(contingence[i1].get(i2, 0))
            matrice_contingence.append(ligne)

        # Test chi2 d'indépendance
        chi2_stat, p_value, dof, expected = stats.chi2_contingency(matrice_contingence)

        # V de Cramér
        n = sum(sum(ligne) for ligne in matrice_contingence)
        cramer_v = np.sqrt(chi2_stat / (n * (min(len(valeurs_i1), len(valeurs_i2)) - 1)))

        print(f"      Chi2 = {chi2_stat:.4f}, p-value = {p_value:.6f}")
        print(f"      V de Cramér = {cramer_v:.4f}")

        return {
            'probabilites_conditionnelles': probas_conditionnelles,
            'contingence': dict(contingence),
            'chi2_stat': chi2_stat,
            'p_value': p_value,
            'cramer_v': cramer_v,
            'valeurs_index1': valeurs_i1,
            'valeurs_index2': valeurs_i2,
            'matrice_contingence': matrice_contingence
        }

    def _analyser_index3_given_index1_index2(self, index1_data, index2_data, index3_data):
        """Analyse P(INDEX3|INDEX1, INDEX2)"""
        print("   Calcul des probabilités conditionnelles P(INDEX3|INDEX1, INDEX2)...")

        # Créer tableau de contingence 3D
        from collections import defaultdict
        contingence_3d = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_3d[i1][i2][i3] += 1

        # Calculer probabilités conditionnelles P(INDEX3|INDEX1, INDEX2)
        probas_conditionnelles = {}

        for i1 in contingence_3d:
            probas_conditionnelles[i1] = {}
            for i2 in contingence_3d[i1]:
                total_i1_i2 = sum(contingence_3d[i1][i2].values())
                probas_conditionnelles[i1][i2] = {}
                for i3 in contingence_3d[i1][i2]:
                    probas_conditionnelles[i1][i2][i3] = contingence_3d[i1][i2][i3] / total_i1_i2

        # Tests chi2 pour chaque combinaison INDEX1-INDEX2
        tests_chi2 = {}

        for i1 in contingence_3d:
            for i2 in contingence_3d[i1]:
                if len(contingence_3d[i1][i2]) > 1:  # Au moins 2 valeurs d'INDEX3
                    # Test si INDEX3 suit distribution uniforme pour cette combinaison
                    observes = list(contingence_3d[i1][i2].values())
                    total = sum(observes)
                    attendus = [total / len(observes)] * len(observes)

                    if all(a >= 5 for a in attendus):  # Condition pour chi2
                        chi2_stat = sum((o - a)**2 / a for o, a in zip(observes, attendus))
                        p_value = 1 - stats.chi2.cdf(chi2_stat, len(observes) - 1)
                        tests_chi2[f"{i1}_{i2}"] = {
                            'chi2_stat': chi2_stat,
                            'p_value': p_value,
                            'observes': observes,
                            'attendus': attendus
                        }

        print(f"      Calculé {len(tests_chi2)} tests chi2 pour combinaisons INDEX1-INDEX2")

        return {
            'probabilites_conditionnelles': probas_conditionnelles,
            'contingence_3d': dict(contingence_3d),
            'tests_chi2': tests_chi2
        }

    def _analyser_influences_causales(self, index1_data, index2_data, index3_data):
        """Analyse des influences causales entre les INDEX"""
        print("   Calcul des mesures d'influence causale...")

        # Distributions marginales
        dist_i1 = Counter(index1_data)
        dist_i2 = Counter(index2_data)
        dist_i3 = Counter(index3_data)

        n_total = len(index1_data)

        # Probabilités marginales
        prob_i1 = {k: v/n_total for k, v in dist_i1.items()}
        prob_i2 = {k: v/n_total for k, v in dist_i2.items()}
        prob_i3 = {k: v/n_total for k, v in dist_i3.items()}

        influences = {}

        # 1. Influence INDEX1 → INDEX2
        print("      Analyse influence INDEX1 → INDEX2...")
        contingence_i1_i2 = defaultdict(lambda: defaultdict(int))
        for i1, i2 in zip(index1_data, index2_data):
            contingence_i1_i2[i1][i2] += 1

        # Calculer écarts par rapport à l'indépendance
        ecarts_i1_i2 = {}
        for i1 in contingence_i1_i2:
            total_i1 = sum(contingence_i1_i2[i1].values())
            ecarts_i1_i2[i1] = {}
            for i2 in contingence_i1_i2[i1]:
                prob_conditionnelle = contingence_i1_i2[i1][i2] / total_i1
                prob_marginale = prob_i2.get(i2, 0)
                ecart = abs(prob_conditionnelle - prob_marginale)
                ecarts_i1_i2[i1][i2] = {
                    'prob_conditionnelle': prob_conditionnelle,
                    'prob_marginale': prob_marginale,
                    'ecart': ecart
                }

        influences['index1_vers_index2'] = ecarts_i1_i2

        # 2. Influence INDEX2 → INDEX3
        print("      Analyse influence INDEX2 → INDEX3...")
        contingence_i2_i3 = defaultdict(lambda: defaultdict(int))
        for i2, i3 in zip(index2_data, index3_data):
            contingence_i2_i3[i2][i3] += 1

        ecarts_i2_i3 = {}
        for i2 in contingence_i2_i3:
            total_i2 = sum(contingence_i2_i3[i2].values())
            ecarts_i2_i3[i2] = {}
            for i3 in contingence_i2_i3[i2]:
                prob_conditionnelle = contingence_i2_i3[i2][i3] / total_i2
                prob_marginale = prob_i3.get(i3, 0)
                ecart = abs(prob_conditionnelle - prob_marginale)
                ecarts_i2_i3[i2][i3] = {
                    'prob_conditionnelle': prob_conditionnelle,
                    'prob_marginale': prob_marginale,
                    'ecart': ecart
                }

        influences['index2_vers_index3'] = ecarts_i2_i3

        # 3. Influence INDEX1 → INDEX3 (direct)
        print("      Analyse influence INDEX1 → INDEX3 (direct)...")
        contingence_i1_i3 = defaultdict(lambda: defaultdict(int))
        for i1, i3 in zip(index1_data, index3_data):
            contingence_i1_i3[i1][i3] += 1

        ecarts_i1_i3 = {}
        for i1 in contingence_i1_i3:
            total_i1 = sum(contingence_i1_i3[i1].values())
            ecarts_i1_i3[i1] = {}
            for i3 in contingence_i1_i3[i1]:
                prob_conditionnelle = contingence_i1_i3[i1][i3] / total_i1
                prob_marginale = prob_i3.get(i3, 0)
                ecart = abs(prob_conditionnelle - prob_marginale)
                ecarts_i1_i3[i1][i3] = {
                    'prob_conditionnelle': prob_conditionnelle,
                    'prob_marginale': prob_marginale,
                    'ecart': ecart
                }

        influences['index1_vers_index3_direct'] = ecarts_i1_i3

        # 4. Information mutuelle
        print("      Calcul information mutuelle...")

        # Information mutuelle INDEX1-INDEX2
        mi_i1_i2 = 0
        for i1 in contingence_i1_i2:
            for i2 in contingence_i1_i2[i1]:
                p_joint = contingence_i1_i2[i1][i2] / n_total
                p_i1 = prob_i1[i1]
                p_i2 = prob_i2[i2]
                if p_joint > 0 and p_i1 > 0 and p_i2 > 0:
                    mi_i1_i2 += p_joint * np.log2(p_joint / (p_i1 * p_i2))

        # Information mutuelle INDEX2-INDEX3
        mi_i2_i3 = 0
        for i2 in contingence_i2_i3:
            for i3 in contingence_i2_i3[i2]:
                p_joint = contingence_i2_i3[i2][i3] / n_total
                p_i2 = prob_i2[i2]
                p_i3 = prob_i3[i3]
                if p_joint > 0 and p_i2 > 0 and p_i3 > 0:
                    mi_i2_i3 += p_joint * np.log2(p_joint / (p_i2 * p_i3))

        influences['information_mutuelle'] = {
            'index1_index2': mi_i1_i2,
            'index2_index3': mi_i2_i3
        }

        return influences

    def _analyser_capacites_predictives(self, index1_data, index2_data, index3_data):
        """Analyse des capacités prédictives du système"""
        print("   Calcul des capacités prédictives...")

        capacites = {}

        # 1. Prédiction INDEX2 à partir d'INDEX1
        print("      Prédiction INDEX2 à partir d'INDEX1...")

        # Calculer argmax(P(INDEX2|INDEX1))
        contingence_i1_i2 = defaultdict(lambda: defaultdict(int))
        for i1, i2 in zip(index1_data, index2_data):
            contingence_i1_i2[i1][i2] += 1

        predictions_i2 = {}
        for i1 in contingence_i1_i2:
            total_i1 = sum(contingence_i1_i2[i1].values())
            probas = {i2: count/total_i1 for i2, count in contingence_i1_i2[i1].items()}
            prediction = max(probas.items(), key=lambda x: x[1])
            predictions_i2[i1] = {
                'prediction': prediction[0],
                'probabilite': prediction[1],
                'toutes_probas': probas
            }

        # Calculer précision de prédiction
        predictions_correctes_i2 = 0
        total_predictions_i2 = 0

        for i1, i2_reel in zip(index1_data, index2_data):
            if i1 in predictions_i2:
                prediction = predictions_i2[i1]['prediction']
                if prediction == i2_reel:
                    predictions_correctes_i2 += 1
                total_predictions_i2 += 1

        precision_i2 = predictions_correctes_i2 / total_predictions_i2 if total_predictions_i2 > 0 else 0

        capacites['prediction_index2'] = {
            'predictions': predictions_i2,
            'precision': precision_i2,
            'predictions_correctes': predictions_correctes_i2,
            'total_predictions': total_predictions_i2
        }

        # 2. Prédiction INDEX3 à partir d'(INDEX1, INDEX2)
        print("      Prédiction INDEX3 à partir d'(INDEX1, INDEX2)...")

        contingence_i1_i2_i3 = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_i1_i2_i3[i1][i2][i3] += 1

        predictions_i3 = {}
        for i1 in contingence_i1_i2_i3:
            predictions_i3[i1] = {}
            for i2 in contingence_i1_i2_i3[i1]:
                total_i1_i2 = sum(contingence_i1_i2_i3[i1][i2].values())
                if total_i1_i2 > 0:
                    probas = {i3: count/total_i1_i2 for i3, count in contingence_i1_i2_i3[i1][i2].items()}
                    prediction = max(probas.items(), key=lambda x: x[1])
                    predictions_i3[i1][i2] = {
                        'prediction': prediction[0],
                        'probabilite': prediction[1],
                        'toutes_probas': probas
                    }

        # Calculer précision de prédiction INDEX3
        predictions_correctes_i3 = 0
        total_predictions_i3 = 0

        for i1, i2, i3_reel in zip(index1_data, index2_data, index3_data):
            if i1 in predictions_i3 and i2 in predictions_i3[i1]:
                prediction = predictions_i3[i1][i2]['prediction']
                if prediction == i3_reel:
                    predictions_correctes_i3 += 1
                total_predictions_i3 += 1

        precision_i3 = predictions_correctes_i3 / total_predictions_i3 if total_predictions_i3 > 0 else 0

        capacites['prediction_index3'] = {
            'predictions': predictions_i3,
            'precision': precision_i3,
            'predictions_correctes': predictions_correctes_i3,
            'total_predictions': total_predictions_i3
        }

        print(f"      Précision prédiction INDEX2 : {precision_i2:.4f}")
        print(f"      Précision prédiction INDEX3 : {precision_i3:.4f}")

        return capacites

    def analyser_toutes_sequences(self):
        """Lance l'analyse complète de toutes les séquences"""
        print("\nLANCEMENT DE L'ANALYSE COMPLÈTE")
        print("=" * 50)

        # 1. Analyse des runs (analyse originale)
        print("\nPHASE 1: ANALYSE DES RUNS")
        print("-" * 30)

        for nom_index, sequence in self.sequences.items():
            if nom_index in ['INDEX1', 'INDEX2', 'INDEX3']:  # Analyser seulement les INDEX principaux
                print(f"\nAnalyse des runs {nom_index}")

                # Analyse des runs
                resultats_runs = self.analyser_runs(sequence, nom_index)

                # Autocorrélation
                print(f"Calcul de l'autocorrélation...")
                autocorr = self.calculer_autocorrelation(sequence)

                # Entropie de Shannon
                print(f"Calcul de l'entropie de Shannon...")
                entropie = self.calculer_entropie_shannon(sequence)

                # Stocker les résultats
                self.resultats[nom_index] = {
                    'runs': resultats_runs,
                    'autocorrelation': autocorr,
                    'entropie_shannon': entropie,
                    'taille_sequence': len(sequence)
                }

                print(f"Analyse {nom_index} terminée")

        # 2. Analyse des probabilités conditionnelles (nouvelle analyse)
        print("\nPHASE 2: ANALYSE DES PROBABILITÉS CONDITIONNELLES")
        print("-" * 50)

        resultats_probas = self.analyser_probabilites_conditionnelles()
        self.resultats['probabilites_conditionnelles'] = resultats_probas

        print("\nAnalyse complète terminée")

    def generer_rapport(self, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des analyses

        Args:
            fichier_sortie: Nom du fichier de rapport (optionnel)
        """
        if not fichier_sortie:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fichier_sortie = f"rapport_analyse_sequences_{timestamp}.txt"

        print(f"\n📝 Génération du rapport : {fichier_sortie}")

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("RAPPORT D'ANALYSE STATISTIQUE DES SÉQUENCES LUPASCO\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date d'analyse : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Fichier source : {self.fichier_json}\n")
            f.write(f"Nombre de parties : {self.nb_parties_total:,}\n\n")

            # Rapport pour chaque index
            for nom_index, resultats in self.resultats.items():
                if nom_index == 'probabilites_conditionnelles':
                    continue  # Traité séparément plus bas

                f.write(f"\n{'='*20} ANALYSE {nom_index} {'='*20}\n\n")
                f.write(f"Taille de la séquence : {resultats['taille_sequence']:,} éléments\n")
                f.write(f"Entropie de Shannon : {resultats['entropie_shannon']:.4f} bits\n\n")

                # Analyse des runs par valeur
                f.write("ANALYSE DES RUNS PAR VALEUR :\n")
                f.write("-" * 40 + "\n")

                for valeur, stats in resultats['runs'].items():
                    if valeur == 'global':
                        continue

                    f.write(f"\n🎯 {valeur} :\n")

                    # Statistiques descriptives
                    stats_desc = stats['statistiques']
                    f.write(f"   Nombre de runs : {stats_desc['nombre_runs']:,}\n")
                    f.write(f"   Longueur moyenne : {stats_desc['longueur_moyenne']:.2f}\n")
                    f.write(f"   Longueur médiane : {stats_desc['longueur_mediane']:.2f}\n")
                    f.write(f"   Longueur max : {stats_desc['longueur_max']}\n")
                    f.write(f"   Écart-type : {stats_desc['ecart_type']:.2f}\n")

                    # Comparaison théorique
                    theorique = stats['theorique']
                    f.write(f"   Probabilité : {theorique['probabilite']:.4f}\n")
                    f.write(f"   Longueur moyenne théorique : {theorique['longueur_moyenne_theorique']:.2f}\n")

                    # Écarts
                    ecarts = stats['ecarts']
                    f.write(f"   Écart longueur moyenne : {ecarts['ecart_longueur_moyenne']:.2f}\n")

                    # Tests statistiques
                    if 'ks_test' in stats['tests'] and stats['tests']['ks_test']['p_value']:
                        ks = stats['tests']['ks_test']
                        f.write(f"   Test KS p-value : {ks['p_value']:.4f}\n")
                        f.write(f"   Test KS significatif : {'Oui' if ks['p_value'] < 0.05 else 'Non'}\n")

                    # Distribution complète des longueurs
                    f.write("   Distribution complète des longueurs :\n")
                    dist_sorted = sorted(stats_desc['distribution'].items(), key=lambda x: x[0])  # Tri par longueur
                    for longueur, count in dist_sorted:
                        f.write(f"     Longueur {longueur} : {count:,} fois\n")

                # Analyse globale
                if 'global' in resultats['runs']:
                    global_stats = resultats['runs']['global']
                    f.write(f"\nANALYSE GLOBALE :\n")
                    f.write("-" * 20 + "\n")
                    f.write(f"Nombre total de runs : {global_stats['nombre_total_runs']:,}\n")
                    f.write(f"Longueur moyenne globale : {global_stats['longueur_moyenne_globale']:.2f}\n")
                    f.write(f"Longueur max globale : {global_stats['longueur_max_globale']}\n")

                    # Runs test
                    if 'runs_test' in global_stats:
                        rt = global_stats['runs_test']
                        f.write(f"\nRUNS TEST (Test de randomness) :\n")
                        f.write(f"   Runs observés : {rt['runs_observes']}\n")
                        f.write(f"   Runs attendus : {rt['runs_attendus']:.2f}\n")
                        f.write(f"   Z-score : {rt['z_score']:.4f}\n")
                        f.write(f"   P-value : {rt['p_value']:.4f}\n")
                        f.write(f"   Significatif : {'Oui' if rt['significatif'] else 'Non'}\n")

                # Autocorrélation
                f.write(f"\nAUTOCORRÉLATION :\n")
                f.write("-" * 15 + "\n")
                autocorr = resultats['autocorrelation']
                for lag in sorted(autocorr.keys())[:10]:  # Premiers 10 lags
                    f.write(f"   Lag {lag} : {autocorr[lag]:.4f}\n")

            # Rapport des probabilités conditionnelles
            if 'probabilites_conditionnelles' in self.resultats:
                f.write(f"\n{'='*60}\n")
                f.write("ANALYSE DES PROBABILITÉS CONDITIONNELLES\n")
                f.write("="*60 + "\n\n")

                probas_results = self.resultats['probabilites_conditionnelles']

                # INDEX2 en fonction d'INDEX1
                if 'index2_given_index1' in probas_results:
                    f.write("1. ANALYSE INDEX2 en fonction d'INDEX1\n")
                    f.write("-" * 40 + "\n")

                    i2_i1 = probas_results['index2_given_index1']
                    f.write(f"Test d'indépendance Chi² = {i2_i1['chi2_stat']:.4f}\n")
                    f.write(f"P-value = {i2_i1['p_value']:.6f}\n")
                    f.write(f"V de Cramér = {i2_i1['cramer_v']:.4f}\n")
                    f.write(f"Indépendance : {'Rejetée' if i2_i1['p_value'] < 0.05 else 'Acceptée'}\n\n")

                    f.write("Probabilités conditionnelles P(INDEX2|INDEX1) :\n")
                    for i1, probas in i2_i1['probabilites_conditionnelles'].items():
                        f.write(f"   {i1} :\n")
                        for i2, proba in probas.items():
                            f.write(f"      P({i2}|{i1}) = {proba:.4f}\n")
                        f.write("\n")

                # INDEX3 en fonction d'(INDEX1, INDEX2)
                if 'index3_given_index1_index2' in probas_results:
                    f.write("2. ANALYSE INDEX3 en fonction d'(INDEX1, INDEX2)\n")
                    f.write("-" * 45 + "\n")

                    i3_i1_i2 = probas_results['index3_given_index1_index2']
                    f.write("Probabilités conditionnelles P(INDEX3|INDEX1, INDEX2) :\n")

                    for i1, dict_i2 in i3_i1_i2['probabilites_conditionnelles'].items():
                        f.write(f"   {i1} :\n")
                        for i2, probas_i3 in dict_i2.items():
                            f.write(f"      {i2} :\n")
                            for i3, proba in probas_i3.items():
                                f.write(f"         P({i3}|{i1},{i2}) = {proba:.4f}\n")
                        f.write("\n")

                # Influences causales
                if 'influences_causales' in probas_results:
                    f.write("3. MESURES D'INFLUENCE CAUSALE\n")
                    f.write("-" * 30 + "\n")

                    influences = probas_results['influences_causales']

                    if 'information_mutuelle' in influences:
                        mi = influences['information_mutuelle']
                        f.write("Information mutuelle :\n")
                        f.write(f"   I(INDEX1; INDEX2) = {mi['index1_index2']:.4f} bits\n")
                        f.write(f"   I(INDEX2; INDEX3) = {mi['index2_index3']:.4f} bits\n\n")

                # Capacités prédictives
                if 'analyses_predictives' in probas_results:
                    f.write("4. CAPACITÉS PRÉDICTIVES\n")
                    f.write("-" * 25 + "\n")

                    pred = probas_results['analyses_predictives']

                    if 'prediction_index2' in pred:
                        p_i2 = pred['prediction_index2']
                        f.write(f"Prédiction INDEX2 à partir d'INDEX1 :\n")
                        f.write(f"   Précision = {p_i2['precision']:.4f}\n")
                        f.write(f"   Prédictions correctes = {p_i2['predictions_correctes']:,}\n")
                        f.write(f"   Total prédictions = {p_i2['total_predictions']:,}\n\n")

                    if 'prediction_index3' in pred:
                        p_i3 = pred['prediction_index3']
                        f.write(f"Prédiction INDEX3 à partir d'(INDEX1, INDEX2) :\n")
                        f.write(f"   Précision = {p_i3['precision']:.4f}\n")
                        f.write(f"   Prédictions correctes = {p_i3['predictions_correctes']:,}\n")
                        f.write(f"   Total prédictions = {p_i3['total_predictions']:,}\n\n")

        print(f"Rapport généré : {fichier_sortie}")
        return fichier_sortie

    def generer_graphiques(self, dossier_sortie: str = "graphiques_sequences"):
        """
        Génère des graphiques d'analyse

        Args:
            dossier_sortie: Dossier pour sauvegarder les graphiques
        """
        if not HAS_MATPLOTLIB:
            print("⚠️  Matplotlib non disponible, graphiques non générés")
            return

        import os

        if not os.path.exists(dossier_sortie):
            os.makedirs(dossier_sortie)

        print(f"\n📊 Génération des graphiques dans : {dossier_sortie}")

        for nom_index, resultats in self.resultats.items():
            # Ignorer les résultats qui ne sont pas des analyses de runs
            if nom_index == 'probabilites_conditionnelles' or 'runs' not in resultats:
                continue

            # Graphique des distributions de longueurs de runs
            plt.figure(figsize=(12, 8))

            subplot_idx = 1
            valeurs = [v for v in resultats['runs'].keys() if v != 'global']
            n_valeurs = len(valeurs)

            for valeur in valeurs:
                plt.subplot(2, (n_valeurs + 1) // 2, subplot_idx)

                stats_desc = resultats['runs'][valeur]['statistiques']
                distribution = stats_desc['distribution']

                longueurs = list(distribution.keys())
                frequences = list(distribution.values())

                plt.bar(longueurs, frequences, alpha=0.7)
                plt.title(f'Distribution runs {valeur}')
                plt.xlabel('Longueur du run')
                plt.ylabel('Fréquence')
                plt.grid(True, alpha=0.3)

                subplot_idx += 1

            plt.tight_layout()
            plt.savefig(f"{dossier_sortie}/distribution_runs_{nom_index}.png", dpi=300, bbox_inches='tight')
            plt.close()

            # Graphique d'autocorrélation
            if 'autocorrelation' in resultats:
                plt.figure(figsize=(10, 6))
                autocorr = resultats['autocorrelation']
                lags = list(autocorr.keys())
                correlations = list(autocorr.values())

                plt.plot(lags, correlations, 'bo-', markersize=4)
                plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
                plt.title(f'Autocorrélation - {nom_index}')
                plt.xlabel('Lag')
                plt.ylabel('Coefficient d\'autocorrélation')
                plt.grid(True, alpha=0.3)

                plt.savefig(f"{dossier_sortie}/autocorrelation_{nom_index}.png", dpi=300, bbox_inches='tight')
                plt.close()

        print(f"✅ Graphiques générés dans {dossier_sortie}/")


def main():
    """Fonction principale du programme"""
    import sys

    print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
    print("=" * 60)
    print("Basé sur : recherche_analyse_statistique_sequences.txt")
    print()

    # Vérifier les arguments
    if len(sys.argv) < 2:
        print("❌ Erreur : Fichier JSON requis")
        print("\nUsage:")
        print("  python analyseur_sequences_lupasco.py <fichier_json>")
        print("\nExemple:")
        print("  python analyseur_sequences_lupasco.py dataset_baccarat_lupasco_20250617_232800.json")
        sys.exit(1)

    fichier_json = sys.argv[1]

    try:
        # Créer l'analyseur
        analyseur = AnalyseurSequencesLupasco(fichier_json)

        # Charger les données
        analyseur.charger_donnees()

        # Lancer l'analyse complète
        analyseur.analyser_toutes_sequences()

        # Générer le rapport
        fichier_rapport = analyseur.generer_rapport()

        # Générer les graphiques
        try:
            analyseur.generer_graphiques()
        except ImportError:
            print("⚠️  Matplotlib non disponible, graphiques non générés")
        except Exception as e:
            print(f"⚠️  Erreur lors de la génération des graphiques : {e}")

        print(f"\n🎉 ANALYSE TERMINÉE AVEC SUCCÈS !")
        print(f"📝 Rapport détaillé : {fichier_rapport}")
        print(f"📊 Graphiques : dossier graphiques_sequences/")

    except Exception as e:
        print(f"\n❌ ERREUR : {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
