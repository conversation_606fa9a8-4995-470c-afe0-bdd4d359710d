#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ANALYSEUR STATISTIQUE DES SÉQUENCES CONSÉCUTIVES - SYSTÈME LUPASCO
===================================================================

Programme d'analyse statistique des séquences consécutives basé sur la recherche
académique pour le dataset Baccarat Lupasco de 100 000 parties.

Analyse les runs/séquences de :
- SYNC/DESYNC (INDEX1)
- pair_4/pair_6/impair_5 (INDEX2)  
- PLAYER/BANKER (INDEX3)

Basé sur : recherche_analyse_statistique_sequences.txt
"""

import json
import numpy as np
from scipy import stats
from scipy.stats import chi2, kstest, geom, fisher_exact, contingency
from collections import Counter, defaultdict
from itertools import combinations
from datetime import datetime
import warnings
import gc
import os
warnings.filterwarnings('ignore')

# Imports optionnels
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False

# Parser JSON streaming pour gros fichiers
try:
    import ijson
    HAS_IJSON = True
except ImportError:
    HAS_IJSON = False


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences consécutives pour le système Lupasco
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec le fichier JSON
        
        Args:
            fichier_json: Chemin vers le fichier JSON du dataset Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        self.nb_parties_total = 0
        
        print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
        print("=" * 60)
        print(f"📂 Fichier à analyser : {fichier_json}")
        
    def charger_donnees(self):
        """Charge et extrait les données du fichier JSON volumineux (7GB) avec streaming"""
        print("\n📊 Chargement des données...")

        try:
            # Obtenir la taille du fichier
            taille_fichier = os.path.getsize(self.fichier_json)
            taille_gb = taille_fichier / (1024 * 1024 * 1024)
            taille_mb = taille_fichier / (1024 * 1024)

            if taille_gb >= 1:
                print(f"📁 Taille du fichier : {taille_gb:.1f} GB")
                print("🚨 FICHIER TRÈS VOLUMINEUX DÉTECTÉ !")

                if HAS_IJSON:
                    print("⚡ Utilisation du mode streaming ijson pour éviter les problèmes de mémoire...")
                    self._charger_avec_streaming()
                else:
                    print("❌ Module ijson requis pour les fichiers > 1 GB")
                    print("💡 Installation : pip install ijson")
                    raise ImportError("ijson requis pour les gros fichiers")
            else:
                print(f"📁 Taille du fichier : {taille_mb:.1f} MB")
                print("🔄 Chargement standard...")
                self._charger_standard()

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise

    def _charger_avec_streaming(self):
        """Charge le fichier JSON avec streaming ijson (pour fichiers 7GB)"""
        print("🔄 Lecture streaming du fichier JSON...")

        # Initialiser les listes de séquences directement
        index1_seq = []
        index2_seq = []
        index3_seq = []
        index1_index2_seq = []
        index1_index3_seq = []
        index2_index3_seq = []
        index1_index2_index3_seq = []

        total_parties = 0
        total_mains = 0

        # Nettoyage mémoire préalable
        gc.collect()

        try:
            with open(self.fichier_json, 'rb') as f:
                print("   📖 Parsing streaming des parties...")

                # Parser streaming des parties
                parties = ijson.items(f, 'parties.item')

                for partie in parties:
                    total_parties += 1

                    # Traiter chaque main de la partie
                    for main in partie['mains']:
                        idx1 = main['index1_sync_state']
                        idx2 = main['index2_cards_category']
                        idx3 = main['index3_result']

                        # INDEX individuels
                        index1_seq.append(idx1)
                        index2_seq.append(idx2)

                        # INDEX3 : PLAYER/BANKER (exclure TIE)
                        if idx3 in ['PLAYER', 'BANKER']:
                            index3_seq.append(idx3)

                            # Combinaisons avec INDEX3
                            index1_index3_seq.append(f"{idx1}_{idx3}")
                            index2_index3_seq.append(f"{idx2}_{idx3}")
                            index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                        # Combinaisons sans INDEX3
                        index1_index2_seq.append(f"{idx1}_{idx2}")

                        total_mains += 1

                    # Affichage du progrès tous les 5000 parties
                    if total_parties % 5000 == 0:
                        print(f"   📈 Parties traitées : {total_parties:,} - {total_mains:,} mains extraites")
                        gc.collect()  # Nettoyage mémoire périodique

            # Stocker les séquences
            self.sequences = {
                'INDEX1': index1_seq,
                'INDEX2': index2_seq,
                'INDEX3': index3_seq,
                'INDEX1_INDEX2': index1_index2_seq,
                'INDEX1_INDEX3': index1_index3_seq,
                'INDEX2_INDEX3': index2_index3_seq,
                'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
            }

            print(f"✅ Streaming terminé : {total_parties:,} parties, {total_mains:,} mains")
            print(f"   - INDEX1 : {len(index1_seq):,} valeurs")
            print(f"   - INDEX2 : {len(index2_seq):,} valeurs")
            print(f"   - INDEX3 : {len(index3_seq):,} valeurs")
            print(f"   - Combinaisons : {len(index1_index2_seq):,} INDEX1_INDEX2")

            # Stocker le nombre de parties pour le rapport
            self.nb_parties_total = total_parties

            # Nettoyage final
            gc.collect()

        except Exception as e:
            print(f"❌ Erreur streaming : {e}")
            raise

    def _charger_standard(self):
        """Chargement standard pour fichiers plus petits"""
        gc.collect()

        with open(self.fichier_json, 'r', encoding='utf-8') as f:
            print("   📖 Parsing JSON standard...")
            self.donnees = json.load(f)

        nb_parties = len(self.donnees['parties'])
        print(f"✅ {nb_parties:,} parties chargées")

        # Stocker le nombre de parties pour le rapport
        self.nb_parties_total = nb_parties

        # Extraire les séquences
        self._extraire_sequences()

        # Libérer la mémoire du JSON original
        del self.donnees
        gc.collect()
    
    def _extraire_sequences(self):
        """Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties"""
        print("🔍 Extraction des séquences et combinaisons...")

        # Initialiser les listes de séquences individuelles
        index1_seq = []  # SYNC/DESYNC
        index2_seq = []  # pair_4/pair_6/impair_5
        index3_seq = []  # PLAYER/BANKER (exclure TIE pour analyse binaire)

        # Initialiser les listes de séquences combinées
        index1_index2_seq = []  # SYNC/DESYNC + pair_4/pair_6/impair_5
        index1_index3_seq = []  # SYNC/DESYNC + PLAYER/BANKER
        index2_index3_seq = []  # pair_4/pair_6/impair_5 + PLAYER/BANKER
        index1_index2_index3_seq = []  # Combinaison des 3 INDEX

        total_mains = 0
        nb_parties = len(self.donnees['parties'])

        # Définir l'intervalle d'affichage selon le nombre de parties
        if nb_parties <= 1000:
            intervalle = 100
        elif nb_parties <= 10000:
            intervalle = 1000
        else:
            intervalle = 10000  # Pour 100 000 parties, affichage tous les 10 000

        print(f"📊 Traitement de {nb_parties:,} parties (affichage tous les {intervalle:,})...")

        for i, partie in enumerate(self.donnees['parties'], 1):
            for main in partie['mains']:
                # INDEX individuels
                idx1 = main['index1_sync_state']
                idx2 = main['index2_cards_category']
                idx3 = main['index3_result']

                index1_seq.append(idx1)
                index2_seq.append(idx2)

                # INDEX3 : PLAYER/BANKER (exclure TIE pour runs binaires)
                if idx3 in ['PLAYER', 'BANKER']:
                    index3_seq.append(idx3)

                    # Combinaisons avec INDEX3 (seulement si pas TIE)
                    index1_index3_seq.append(f"{idx1}_{idx3}")
                    index2_index3_seq.append(f"{idx2}_{idx3}")
                    index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                # Combinaisons sans INDEX3 (toujours possibles)
                index1_index2_seq.append(f"{idx1}_{idx2}")

                total_mains += 1

            # Affichage cyclique du progrès
            if i % intervalle == 0 or i == nb_parties:
                pourcentage = (i / nb_parties) * 100
                print(f"   📈 Parties traitées : {i:,}/{nb_parties:,} ({pourcentage:.1f}%) - {total_mains:,} mains extraites")

        self.sequences = {
            # INDEX individuels
            'INDEX1': index1_seq,
            'INDEX2': index2_seq,
            'INDEX3': index3_seq,

            # Combinaisons d'INDEX
            'INDEX1_INDEX2': index1_index2_seq,
            'INDEX1_INDEX3': index1_index3_seq,
            'INDEX2_INDEX3': index2_index3_seq,
            'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
        }

        print(f"✅ {total_mains:,} mains extraites")
        print(f"   - INDEX1 (SYNC/DESYNC) : {len(index1_seq):,} valeurs")
        print(f"   - INDEX2 (pair_4/6/impair_5) : {len(index2_seq):,} valeurs")
        print(f"   - INDEX3 (PLAYER/BANKER) : {len(index3_seq):,} valeurs")
        print(f"   - INDEX1_INDEX2 : {len(index1_index2_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX3 : {len(index1_index3_seq):,} combinaisons")
        print(f"   - INDEX2_INDEX3 : {len(index2_index3_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX2_INDEX3 : {len(index1_index2_index3_seq):,} combinaisons")
    
    def analyser_runs(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse les runs (séquences consécutives) d'une séquence
        
        Args:
            sequence: Liste des valeurs de la séquence
            nom_sequence: Nom de la séquence pour l'affichage
            
        Returns:
            dict: Résultats de l'analyse des runs
        """
        print(f"\n🔬 Analyse des runs : {nom_sequence}")
        
        # Identifier les runs
        runs = []
        if not sequence:
            return {}
        
        current_value = sequence[0]
        current_length = 1
        
        for i in range(1, len(sequence)):
            if sequence[i] == current_value:
                current_length += 1
            else:
                runs.append((current_value, current_length))
                current_value = sequence[i]
                current_length = 1
        
        # Ajouter le dernier run
        runs.append((current_value, current_length))
        
        # Analyser par type de valeur
        resultats = {}
        valeurs_uniques = list(set(sequence))
        
        for valeur in valeurs_uniques:
            longueurs_runs = [length for value, length in runs if value == valeur]
            
            if longueurs_runs:
                resultats[valeur] = self._analyser_runs_valeur(
                    longueurs_runs, valeur, len(sequence), sequence.count(valeur)
                )
        
        # Statistiques globales
        resultats['global'] = self._analyser_runs_global(runs, sequence)
        
        return resultats
    
    def _analyser_runs_valeur(self, longueurs: list, valeur: str, n_total: int, n_valeur: int) -> dict:
        """Analyse les runs pour une valeur spécifique"""
        
        # Statistiques descriptives
        stats_desc = {
            'nombre_runs': len(longueurs),
            'longueur_moyenne': np.mean(longueurs),
            'longueur_mediane': np.median(longueurs),
            'longueur_max': max(longueurs),
            'longueur_min': min(longueurs),
            'ecart_type': np.std(longueurs),
            'distribution': Counter(longueurs)
        }
        
        # Probabilité théorique
        p = n_valeur / n_total
        
        # Longueur moyenne théorique (distribution géométrique)
        longueur_moyenne_theorique = 1 / p if p > 0 else float('inf')
        
        # Nombre de runs théorique
        # Formule : E[R] ≈ 2np(1-p) + 1 pour le nombre total de runs
        # Pour une valeur spécifique : approximativement n_valeur / longueur_moyenne_theorique
        nombre_runs_theorique = n_valeur / longueur_moyenne_theorique if longueur_moyenne_theorique != float('inf') else 0
        
        # Tests statistiques
        tests = {}
        
        # Test de Kolmogorov-Smirnov contre distribution géométrique
        if p > 0 and p < 1:
            # Distribution géométrique théorique
            longueurs_max = max(longueurs)
            x_theorique = np.arange(1, longueurs_max + 1)
            cdf_theorique = geom.cdf(x_theorique, p)
            
            # CDF empirique
            longueurs_sorted = np.sort(longueurs)
            cdf_empirique = np.arange(1, len(longueurs) + 1) / len(longueurs)
            
            # Test KS (approximation)
            try:
                ks_stat, ks_pvalue = kstest(longueurs, lambda x: geom.cdf(x, p))
                tests['ks_test'] = {'statistic': ks_stat, 'p_value': ks_pvalue}
            except:
                tests['ks_test'] = {'statistic': None, 'p_value': None}
        
        return {
            'statistiques': stats_desc,
            'theorique': {
                'probabilite': p,
                'longueur_moyenne_theorique': longueur_moyenne_theorique,
                'nombre_runs_theorique': nombre_runs_theorique
            },
            'tests': tests,
            'ecarts': {
                'ecart_longueur_moyenne': stats_desc['longueur_moyenne'] - longueur_moyenne_theorique,
                'ecart_nombre_runs': stats_desc['nombre_runs'] - nombre_runs_theorique
            }
        }
    
    def _analyser_runs_global(self, runs: list, sequence: list) -> dict:
        """Analyse globale des runs"""
        
        # Nombre total de runs
        nombre_total_runs = len(runs)
        
        # Longueurs de tous les runs
        toutes_longueurs = [length for _, length in runs]
        
        # Statistiques globales
        stats_globales = {
            'nombre_total_runs': nombre_total_runs,
            'longueur_moyenne_globale': np.mean(toutes_longueurs),
            'longueur_max_globale': max(toutes_longueurs),
            'distribution_globale': Counter(toutes_longueurs)
        }
        
        # Runs Test (test de randomness)
        # Implémentation simplifiée du runs test
        n = len(sequence)
        valeurs_uniques = list(set(sequence))
        
        if len(valeurs_uniques) == 2:
            # Runs test pour séquence binaire
            n1 = sequence.count(valeurs_uniques[0])
            n2 = sequence.count(valeurs_uniques[1])
            
            # Nombre de runs observé
            R = nombre_total_runs
            
            # Moyenne et variance théoriques
            mu_R = (2 * n1 * n2) / (n1 + n2) + 1
            sigma2_R = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2)**2 * (n1 + n2 - 1))
            sigma_R = np.sqrt(sigma2_R)
            
            # Z-score
            if sigma_R > 0:
                z_score = (R - mu_R) / sigma_R
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            else:
                z_score = 0
                p_value = 1
            
            stats_globales['runs_test'] = {
                'runs_observes': R,
                'runs_attendus': mu_R,
                'z_score': z_score,
                'p_value': p_value,
                'significatif': p_value < 0.05
            }
        
        return stats_globales
    
    def calculer_autocorrelation(self, sequence: list, max_lag: int = 20) -> dict:
        """
        Calcule l'autocorrélation de la séquence
        
        Args:
            sequence: Séquence à analyser
            max_lag: Nombre maximum de lags à calculer
            
        Returns:
            dict: Coefficients d'autocorrélation
        """
        # Convertir en valeurs numériques pour l'autocorrélation
        valeurs_uniques = list(set(sequence))
        mapping = {val: i for i, val in enumerate(valeurs_uniques)}
        sequence_num = [mapping[val] for val in sequence]
        
        autocorr = {}
        n = len(sequence_num)
        
        for lag in range(1, min(max_lag + 1, n // 4)):
            # Calcul de l'autocorrélation pour le lag donné
            x1 = sequence_num[:-lag]
            x2 = sequence_num[lag:]
            
            if len(x1) > 0 and len(x2) > 0:
                corr = np.corrcoef(x1, x2)[0, 1]
                autocorr[lag] = corr if not np.isnan(corr) else 0
        
        return autocorr
    
    def calculer_entropie_shannon(self, sequence: list) -> float:
        """
        Calcule l'entropie de Shannon de la séquence
        
        Args:
            sequence: Séquence à analyser
            
        Returns:
            float: Entropie de Shannon
        """
        # Compter les fréquences
        compteur = Counter(sequence)
        n = len(sequence)
        
        # Calculer l'entropie
        entropie = 0
        for count in compteur.values():
            p = count / n
            if p > 0:
                entropie -= p * np.log2(p)
        
        return entropie

    def analyser_probabilites_conditionnelles(self):
        """
        Analyse complète des probabilités conditionnelles selon les spécifications Lupasco
        """
        print("\nANALYSE DES PROBABILITÉS CONDITIONNELLES")
        print("=" * 50)

        # Extraire les données individuelles (sans combinaisons pour cette analyse)
        index1_data = self.sequences['INDEX1']
        index2_data = self.sequences['INDEX2']
        index3_data = self.sequences['INDEX3']

        # Créer les données alignées (même longueur)
        min_length = min(len(index1_data), len(index2_data), len(index3_data))
        index1_aligned = index1_data[:min_length]
        index2_aligned = index2_data[:min_length]
        index3_aligned = index3_data[:min_length]

        resultats = {}

        # 1. Analyse INDEX2 en fonction de INDEX1
        print("\n1. ANALYSE INDEX2 en fonction de INDEX1")
        resultats['index2_given_index1'] = self._analyser_index2_given_index1(
            index1_aligned, index2_aligned
        )

        # 2. Analyse INDEX3 en fonction de (INDEX1, INDEX2)
        print("\n2. ANALYSE INDEX3 en fonction de (INDEX1, INDEX2)")
        resultats['index3_given_index1_index2'] = self._analyser_index3_given_index1_index2(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 3. Mesures d'influence causale
        print("\n3. MESURES D'INFLUENCE CAUSALE")
        resultats['influences_causales'] = self._analyser_influences_causales(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 4. Analyses prédictives
        print("\n4. ANALYSES PRÉDICTIVES")
        resultats['analyses_predictives'] = self._analyser_capacites_predictives(
            index1_aligned, index2_aligned, index3_aligned
        )

        return resultats

    def analyser_predictions_lupasco(self):
        """
        Analyse prédictive avec cloisonnement temporel strict
        """
        print("\nANALYSE PRÉDICTIVE LUPASCO CLOISONNÉE")
        print("=" * 50)

        # Extraire les données individuelles
        index1_data = self.sequences['INDEX1']
        index2_data = self.sequences['INDEX2']
        index3_data = self.sequences['INDEX3']

        # Créer les données alignées (même longueur)
        min_length = min(len(index1_data), len(index2_data), len(index3_data))
        index1_aligned = index1_data[:min_length]
        index2_aligned = index2_data[:min_length]
        index3_aligned = index3_data[:min_length]

        resultats_predictions = {}

        # 1. Validation de la règle Lupasco : P(INDEX1(t+1)|INDEX2(t))
        print("\n1. VALIDATION RÈGLE LUPASCO : P(INDEX1(t+1)|INDEX2(t))")
        resultats_predictions['validation_lupasco'] = self._valider_regle_lupasco(
            index1_aligned, index2_aligned
        )

        # 2. Entraînement des modèles prédictifs avec cloisonnement
        print("\n2. ENTRAÎNEMENT MODÈLES PRÉDICTIFS CLOISONNÉS")
        resultats_predictions['modeles_entraines'] = self._entrainer_modeles_cloisonnes(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 3. Validation temporelle avec cloisonnement strict
        print("\n3. VALIDATION TEMPORELLE CLOISONNÉE")
        resultats_predictions['validation_temporelle'] = self._valider_predictions_cloisonnees(
            index1_aligned, index2_aligned, index3_aligned
        )

        return resultats_predictions

    def _entrainer_modeles_cloisonnes(self, index1_data, index2_data, index3_data):
        """
        Entraînement des modèles avec cloisonnement temporel strict
        Utilise seulement les données historiques pour apprendre
        """
        print("   Entraînement avec cloisonnement temporel...")

        # Créer historique d'entraînement (toutes les mains sauf la dernière)
        historique_entrainement = []
        for i in range(len(index1_data) - 1):  # Exclure la dernière main
            historique_entrainement.append({
                'INDEX1': index1_data[i],
                'INDEX2': index2_data[i],
                'INDEX3': index3_data[i]
            })

        print(f"      Taille historique d'entraînement : {len(historique_entrainement):,} mains")

        # 1. Modèle P(INDEX2|INDEX1) - synchrone
        modele_index2 = self._entrainer_modele_index2(historique_entrainement)

        # 2. Modèle P(INDEX3|INDEX1, INDEX2) - synchrone
        modele_index3 = self._entrainer_modele_index3(historique_entrainement)

        return {
            'modele_index2': modele_index2,
            'modele_index3': modele_index3,
            'taille_entrainement': len(historique_entrainement)
        }

    def _entrainer_modele_index2(self, historique):
        """
        Entraîner P(INDEX2|INDEX1) sur données historiques uniquement
        """
        print("      Entraînement modèle INDEX2...")

        from collections import defaultdict
        contingence_index2 = defaultdict(lambda: defaultdict(int))

        # Compter occurrences dans l'historique
        for main in historique:
            i1 = main['INDEX1']
            i2 = main['INDEX2']
            contingence_index2[i1][i2] += 1

        # Calculer probabilités P(INDEX2|INDEX1)
        probabilites_index2 = {}
        for i1 in contingence_index2:
            total_i1 = sum(contingence_index2[i1].values())
            probabilites_index2[i1] = {}
            for i2 in contingence_index2[i1]:
                probabilites_index2[i1][i2] = contingence_index2[i1][i2] / total_i1

        # Identifier INDEX2_most_likely pour chaque INDEX1
        predictions_index2 = {}
        for i1 in probabilites_index2:
            probas = probabilites_index2[i1]
            most_likely = max(probas.items(), key=lambda x: x[1])
            predictions_index2[i1] = {
                'most_likely': most_likely[0],
                'probabilite': most_likely[1],
                'toutes_probas': probas
            }

        print(f"         Modèle INDEX2 entraîné sur {sum(sum(d.values()) for d in contingence_index2.values()):,} exemples")

        return {
            'probabilites': probabilites_index2,
            'predictions': predictions_index2,
            'contingence': dict(contingence_index2)
        }

    def _entrainer_modele_index3(self, historique):
        """
        Entraîner P(INDEX3|INDEX1, INDEX2) sur données historiques uniquement
        """
        print("      Entraînement modèle INDEX3...")

        from collections import defaultdict
        contingence_index3 = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        # Compter occurrences dans l'historique
        for main in historique:
            i1 = main['INDEX1']
            i2 = main['INDEX2']
            i3 = main['INDEX3']
            contingence_index3[i1][i2][i3] += 1

        # Calculer probabilités P(INDEX3|INDEX1, INDEX2)
        probabilites_index3 = {}
        predictions_index3 = {}

        for i1 in contingence_index3:
            probabilites_index3[i1] = {}
            predictions_index3[i1] = {}

            for i2 in contingence_index3[i1]:
                total_i1_i2 = sum(contingence_index3[i1][i2].values())
                probabilites_index3[i1][i2] = {}

                # Calculer probabilités pour chaque INDEX3
                for i3 in contingence_index3[i1][i2]:
                    probabilites_index3[i1][i2][i3] = contingence_index3[i1][i2][i3] / total_i1_i2

                # Identifier INDEX3_most_likely
                if contingence_index3[i1][i2]:
                    probas_i3 = probabilites_index3[i1][i2]
                    most_likely = max(probas_i3.items(), key=lambda x: x[1])
                    predictions_index3[i1][i2] = {
                        'most_likely': most_likely[0],
                        'probabilite': most_likely[1],
                        'toutes_probas': probas_i3
                    }

        total_exemples = sum(sum(sum(d2.values()) for d2 in d1.values()) for d1 in contingence_index3.values())
        print(f"         Modèle INDEX3 entraîné sur {total_exemples:,} exemples")

        return {
            'probabilites': probabilites_index3,
            'predictions': predictions_index3,
            'contingence': dict(contingence_index3)
        }

    def _valider_predictions_cloisonnees(self, index1_data, index2_data, index3_data):
        """
        Validation temporelle avec cloisonnement strict
        Teste les prédictions sans accès aux données futures
        """
        print("   Validation avec cloisonnement temporel strict...")

        # Split temporel : 80% entraînement, 20% test
        split_point = int(0.8 * len(index1_data))

        # Données d'entraînement (80% premières mains)
        train_index1 = index1_data[:split_point]
        train_index2 = index2_data[:split_point]
        train_index3 = index3_data[:split_point]

        # Données de test (20% dernières mains)
        test_index1 = index1_data[split_point:]
        test_index2 = index2_data[split_point:]
        test_index3 = index3_data[split_point:]

        print(f"      Split temporel : {len(train_index1):,} entraînement, {len(test_index1):,} test")

        # Entraîner modèles sur données d'entraînement uniquement
        historique_train = []
        for i in range(len(train_index1)):
            historique_train.append({
                'INDEX1': train_index1[i],
                'INDEX2': train_index2[i],
                'INDEX3': train_index3[i]
            })

        modele_index2_train = self._entrainer_modele_index2(historique_train)
        modele_index3_train = self._entrainer_modele_index3(historique_train)

        # Tester prédictions avec cloisonnement strict
        resultats_test = self._tester_predictions_cloisonnees(
            test_index1, test_index2, test_index3,
            modele_index2_train, modele_index3_train
        )

        return {
            'split_point': split_point,
            'taille_train': len(train_index1),
            'taille_test': len(test_index1),
            'modele_index2_train': modele_index2_train,
            'modele_index3_train': modele_index3_train,
            'resultats_test': resultats_test
        }

    def _tester_predictions_cloisonnees(self, test_index1, test_index2, test_index3,
                                       modele_index2, modele_index3):
        """
        Tester prédictions avec accès seulement à INDEX1 de chaque main
        """
        print("      Test prédictions avec cloisonnement...")

        predictions_correctes_index2 = 0
        predictions_correctes_index3 = 0
        total_predictions = 0

        # Métriques détaillées
        from collections import defaultdict
        confusion_matrix_index2 = defaultdict(lambda: defaultdict(int))
        confusion_matrix_index3 = defaultdict(lambda: defaultdict(int))

        predictions_index2_list = []
        predictions_index3_list = []
        reels_index2_list = []
        reels_index3_list = []

        # Pour chaque main de test
        for i in range(len(test_index1)):
            index1_actuel = test_index1[i]
            index2_reel = test_index2[i]
            index3_reel = test_index3[i]

            # PRÉDICTION CLOISONNÉE : Accès seulement à INDEX1
            prediction_complete = self._predire_main_cloisonnee(
                index1_actuel, modele_index2, modele_index3
            )

            if prediction_complete:
                index2_predit = prediction_complete['INDEX2_prediction']
                index3_predit = prediction_complete['INDEX3_prediction']

                # Vérifier prédictions INDEX2
                if index2_predit == index2_reel:
                    predictions_correctes_index2 += 1
                confusion_matrix_index2[index2_reel][index2_predit] += 1

                # Vérifier prédictions INDEX3
                if index3_predit == index3_reel:
                    predictions_correctes_index3 += 1
                confusion_matrix_index3[index3_reel][index3_predit] += 1

                # Stocker pour métriques
                predictions_index2_list.append(index2_predit)
                predictions_index3_list.append(index3_predit)
                reels_index2_list.append(index2_reel)
                reels_index3_list.append(index3_reel)

                total_predictions += 1

        # Calculer métriques finales
        precision_index2 = predictions_correctes_index2 / total_predictions if total_predictions > 0 else 0
        precision_index3 = predictions_correctes_index3 / total_predictions if total_predictions > 0 else 0

        print(f"         Précision INDEX2 cloisonnée : {precision_index2:.4f}")
        print(f"         Précision INDEX3 cloisonnée : {precision_index3:.4f}")
        print(f"         Total prédictions testées : {total_predictions:,}")

        return {
            'precision_index2': precision_index2,
            'precision_index3': precision_index3,
            'predictions_correctes_index2': predictions_correctes_index2,
            'predictions_correctes_index3': predictions_correctes_index3,
            'total_predictions': total_predictions,
            'confusion_matrix_index2': dict(confusion_matrix_index2),
            'confusion_matrix_index3': dict(confusion_matrix_index3),
            'predictions_index2': predictions_index2_list,
            'predictions_index3': predictions_index3_list,
            'reels_index2': reels_index2_list,
            'reels_index3': reels_index3_list
        }

    def _predire_main_cloisonnee(self, index1_actuel, modele_index2, modele_index3):
        """
        Prédiction cloisonnée : accès seulement à INDEX1 de la main actuelle
        """
        # Étape 1 : Prédire INDEX2 avec seulement INDEX1
        if index1_actuel in modele_index2['predictions']:
            index2_prediction = modele_index2['predictions'][index1_actuel]
            index2_predit = index2_prediction['most_likely']

            # Étape 2 : Prédire INDEX3 avec INDEX1 + INDEX2 prédit
            if (index1_actuel in modele_index3['predictions'] and
                index2_predit in modele_index3['predictions'][index1_actuel]):

                index3_prediction = modele_index3['predictions'][index1_actuel][index2_predit]
                index3_predit = index3_prediction['most_likely']

                return {
                    'INDEX2_prediction': index2_predit,
                    'INDEX2_probabilite': index2_prediction['probabilite'],
                    'INDEX3_prediction': index3_predit,
                    'INDEX3_probabilite': index3_prediction['probabilite']
                }

        return None

    def predire_main_suivante_cloisonnee(self, index1_actuel, resultats_predictions):
        """
        Prédiction cloisonnée : accès seulement à INDEX1 actuel
        Utilise les modèles entraînés avec cloisonnement temporel
        """
        print(f"\nPRÉDICTION MAIN CLOISONNÉE - INDEX1 actuel : {index1_actuel}")
        print("-" * 60)

        # Vérifier que les modèles cloisonnés sont disponibles
        if 'modeles_entraines' not in resultats_predictions:
            print("❌ Modèles cloisonnés non disponibles")
            return None

        modeles = resultats_predictions['modeles_entraines']
        modele_index2 = modeles['modele_index2']
        modele_index3 = modeles['modele_index3']

        # Prédiction cloisonnée stricte
        prediction = self._predire_main_cloisonnee(index1_actuel, modele_index2, modele_index3)

        if prediction:
            print(f"✅ Prédiction cloisonnée réussie :")
            print(f"   INDEX1 actuel : {index1_actuel}")
            print(f"   INDEX2 prédit : {prediction['INDEX2_prediction']} (p={prediction['INDEX2_probabilite']:.4f})")
            print(f"   INDEX3 prédit : {prediction['INDEX3_prediction']} (p={prediction['INDEX3_probabilite']:.4f})")

            # Calcul probabilité combinée
            probabilite_combinee = prediction['INDEX2_probabilite'] * prediction['INDEX3_probabilite']

            prediction_complete = {
                'INDEX1_actuel': index1_actuel,
                'INDEX2_prediction': prediction['INDEX2_prediction'],
                'INDEX2_probabilite': prediction['INDEX2_probabilite'],
                'INDEX3_prediction': prediction['INDEX3_prediction'],
                'INDEX3_probabilite': prediction['INDEX3_probabilite'],
                'probabilite_combinee': probabilite_combinee,
                'methode': 'Cloisonnement temporel strict',
                'validation_lupasco': resultats_predictions.get('validation_lupasco', {}).get('precision_lupasco', 'N/A'),
                'taille_entrainement': modeles.get('taille_entrainement', 'N/A')
            }

            print(f"   Probabilité combinée : {probabilite_combinee:.4f}")
            print(f"   Méthode : {prediction_complete['methode']}")
            print(f"   Taille entraînement : {prediction_complete['taille_entrainement']:,} mains")

            return prediction_complete
        else:
            print("❌ Prédiction cloisonnée impossible - INDEX1 non reconnu")
            return None

    def generer_rapport_predictions_optimales(self, resultats_predictions, fichier_sortie=None):
        """
        Génère un rapport détaillé des prédictions selon formules optimales
        """
        if fichier_sortie is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fichier_sortie = f"rapport_predictions_optimales_{timestamp}.txt"

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("RAPPORT PRÉDICTIONS OPTIMALES - SYSTÈME LUPASCO\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Formules : Stanford + Harvard + DataTab + Real Statistics + Paris-Saclay\n\n")

            # 1. Validation règle Lupasco
            if 'validation_lupasco' in resultats_predictions:
                f.write("1. VALIDATION RÈGLE LUPASCO TEMPORELLE\n")
                f.write("-" * 40 + "\n")

                validation = resultats_predictions['validation_lupasco']
                f.write(f"Précision règle Lupasco : {validation['precision_lupasco']:.4f}\n")
                f.write(f"Chi² temporel : {validation['chi2_temporel']:.4f}\n")
                f.write(f"P-value temporel : {validation['p_value_temporel']:.6f}\n")
                f.write(f"V de Cramér temporel : {validation['v_cramer_temporel']:.4f}\n")
                f.write(f"Information mutuelle temporelle : {validation['information_mutuelle_temporelle']:.4f} bits\n")
                f.write(f"Total transitions : {validation['total_transitions']:,}\n\n")

                # Probabilités conditionnelles temporelles
                f.write("Probabilités P(INDEX1(t+1)|INDEX2(t)) :\n")
                for i2, probas in validation['probabilites_conditionnelles_temporelles'].items():
                    f.write(f"   {i2} :\n")
                    for i1, prob in probas.items():
                        f.write(f"      P(INDEX1(t+1)={i1}|INDEX2(t)={i2}) = {prob:.4f}\n")
                f.write("\n")

            # 2. Prédictions INDEX2 synchrones
            if 'prediction_index2' in resultats_predictions:
                f.write("2. PRÉDICTIONS INDEX2 SYNCHRONES\n")
                f.write("-" * 30 + "\n")

                pred_i2 = resultats_predictions['prediction_index2']
                f.write(f"Chi² synchrone : {pred_i2['chi2_synchrone']:.4f}\n")
                f.write(f"P-value synchrone : {pred_i2['p_value_synchrone']:.6f}\n")
                f.write(f"V de Cramér synchrone : {pred_i2['v_cramer_synchrone']:.4f}\n\n")

                f.write("Prédictions INDEX2_most_likely :\n")
                for i1, pred in pred_i2['predictions_index2'].items():
                    f.write(f"   Si INDEX1={i1} → INDEX2={pred['most_likely']} (p={pred['probabilite']:.4f})\n")
                f.write("\n")

            # 3. Prédictions INDEX3 finales
            if 'prediction_index3' in resultats_predictions:
                f.write("3. PRÉDICTIONS INDEX3 FINALES\n")
                f.write("-" * 25 + "\n")

                pred_i3 = resultats_predictions['prediction_index3']
                metriques = pred_i3['metriques_performance']

                f.write(f"Précision globale : {metriques['precision_globale']:.4f}\n")
                f.write(f"F1-Score moyen : {metriques['f1_score_moyen']:.4f}\n")
                f.write(f"V de Cramér performance : {pred_i3['v_cramer_performance']:.4f}\n")
                f.write(f"Prédictions correctes : {metriques['predictions_correctes']:,}\n")
                f.write(f"Total prédictions : {metriques['total_predictions']:,}\n\n")

                # Métriques par classe
                f.write("Métriques par classe INDEX3 :\n")
                for classe, precision in metriques['precision_par_classe'].items():
                    rappel = metriques['rappel_par_classe'][classe]
                    f1 = metriques['f1_par_classe'][classe]
                    f.write(f"   {classe} : Précision={precision:.4f}, Rappel={rappel:.4f}, F1={f1:.4f}\n")
                f.write("\n")

                # Matrice de confusion
                f.write("Matrice de confusion :\n")
                matrice = pred_i3['matrice_confusion']
                classes = sorted(set(classe for dict_pred in matrice.values() for classe in dict_pred.keys()))

                # En-tête
                f.write("Réel\\Prédit".ljust(12))
                for classe in classes:
                    f.write(f"{classe}".ljust(10))
                f.write("\n")

                # Lignes
                for classe_reelle in classes:
                    f.write(f"{classe_reelle}".ljust(12))
                    for classe_predite in classes:
                        count = matrice.get(classe_reelle, {}).get(classe_predite, 0)
                        f.write(f"{count}".ljust(10))
                    f.write("\n")
                f.write("\n")

            # 4. Validation temporelle cloisonnée
            if 'validation_temporelle' in resultats_predictions:
                f.write("4. VALIDATION TEMPORELLE CLOISONNÉE\n")
                f.write("-" * 35 + "\n")

                validation = resultats_predictions['validation_temporelle']
                resultats_test = validation['resultats_test']

                f.write(f"Split temporel : {validation['taille_train']:,} entraînement, {validation['taille_test']:,} test\n")
                f.write(f"Précision INDEX2 cloisonnée : {resultats_test['precision_index2']:.4f}\n")
                f.write(f"Précision INDEX3 cloisonnée : {resultats_test['precision_index3']:.4f}\n")
                f.write(f"Prédictions correctes INDEX2 : {resultats_test['predictions_correctes_index2']:,}\n")
                f.write(f"Prédictions correctes INDEX3 : {resultats_test['predictions_correctes_index3']:,}\n")
                f.write(f"Total prédictions testées : {resultats_test['total_predictions']:,}\n\n")

                # Matrices de confusion cloisonnées
                f.write("Matrice de confusion INDEX2 (cloisonnée) :\n")
                cm_i2 = resultats_test['confusion_matrix_index2']
                classes_i2 = sorted(set(k for d in cm_i2.values() for k in d.keys()) | set(cm_i2.keys()))

                f.write("Réel\\Prédit".ljust(12))
                for classe in classes_i2:
                    f.write(f"{classe}".ljust(12))
                f.write("\n")

                for classe_reelle in classes_i2:
                    f.write(f"{classe_reelle}".ljust(12))
                    for classe_predite in classes_i2:
                        count = cm_i2.get(classe_reelle, {}).get(classe_predite, 0)
                        f.write(f"{count}".ljust(12))
                    f.write("\n")
                f.write("\n")

                f.write("Matrice de confusion INDEX3 (cloisonnée) :\n")
                cm_i3 = resultats_test['confusion_matrix_index3']
                classes_i3 = sorted(set(k for d in cm_i3.values() for k in d.keys()) | set(cm_i3.keys()))

                f.write("Réel\\Prédit".ljust(12))
                for classe in classes_i3:
                    f.write(f"{classe}".ljust(12))
                f.write("\n")

                for classe_reelle in classes_i3:
                    f.write(f"{classe_reelle}".ljust(12))
                    for classe_predite in classes_i3:
                        count = cm_i3.get(classe_reelle, {}).get(classe_predite, 0)
                        f.write(f"{count}".ljust(12))
                    f.write("\n")
                f.write("\n")

            # 5. Exemples de prédictions cloisonnées
            f.write("5. EXEMPLES DE PRÉDICTIONS CLOISONNÉES\n")
            f.write("-" * 40 + "\n")

            # Tester prédictions cloisonnées pour SYNC et DESYNC
            for index1_test in ['SYNC', 'DESYNC']:
                prediction = self.predire_main_suivante_cloisonnee(index1_test, resultats_predictions)
                if prediction:
                    f.write(f"Si INDEX1 actuel = {index1_test} :\n")
                    f.write(f"   → INDEX2 prédit = {prediction['INDEX2_prediction']} (p={prediction['INDEX2_probabilite']:.4f})\n")
                    f.write(f"   → INDEX3 prédit = {prediction['INDEX3_prediction']} (p={prediction['INDEX3_probabilite']:.4f})\n")
                    f.write(f"   → Probabilité combinée = {prediction['probabilite_combinee']:.4f}\n")
                    f.write(f"   → Méthode = {prediction['methode']}\n")
                    f.write(f"   → Taille entraînement = {prediction['taille_entrainement']:,} mains\n\n")

        print(f"Rapport prédictions optimales généré : {fichier_sortie}")
        return fichier_sortie

    def _valider_regle_lupasco(self, index1_data, index2_data):
        """
        Validation de la règle Lupasco selon formules académiques optimales
        P(INDEX1(t+1)|INDEX2(t)) avec tests statistiques rigoureux
        """
        print("   Validation temporelle P(INDEX1(t+1)|INDEX2(t)) selon formules académiques...")

        # Algorithme de comptage temporel (formules optimales)
        transitions_temporelles = self._compter_transitions_temporelles(index1_data, index2_data)

        # Calculer probabilités conditionnelles P(INDEX1(t+1)|INDEX2(t))
        probabilites_temporelles = {}
        totaux_index2_t = {}

        # Calculer totaux pour chaque INDEX2(t)
        for i2_t in transitions_temporelles:
            totaux_index2_t[i2_t] = sum(transitions_temporelles[i2_t].values())

        # Calculer P(INDEX1(t+1)|INDEX2(t)) selon Stanford AMS 110
        for i2_t in transitions_temporelles:
            probabilites_temporelles[i2_t] = {}
            for i1_t_plus_1 in transitions_temporelles[i2_t]:
                # Formule Stanford : P(INDEX1(t+1)|INDEX2(t)) = count(INDEX1(t+1) ET INDEX2(t)) / count(INDEX2(t))
                probabilites_temporelles[i2_t][i1_t_plus_1] = transitions_temporelles[i2_t][i1_t_plus_1] / totaux_index2_t[i2_t]

        # Validation règle Lupasco avec précision académique
        precision_lupasco = self._calculer_precision_lupasco(index1_data, index2_data)

        # Tests statistiques temporels (DataTab + Real Statistics)
        chi2_temporel, p_value_temporel, v_cramer_temporel = self._tests_statistiques_temporels(transitions_temporelles)

        # Information mutuelle temporelle (Harvard)
        mi_temporel = self._information_mutuelle_temporelle(index1_data, index2_data)

        # Vérifications mathématiques (contraintes de probabilité)
        verifications = self._verifier_contraintes_probabilites(probabilites_temporelles)

        validation = {
            'probabilites_conditionnelles_temporelles': probabilites_temporelles,
            'transitions_temporelles': transitions_temporelles,
            'precision_lupasco': precision_lupasco,
            'chi2_temporel': chi2_temporel,
            'p_value_temporel': p_value_temporel,
            'v_cramer_temporel': v_cramer_temporel,
            'information_mutuelle_temporelle': mi_temporel,
            'verifications_mathematiques': verifications,
            'total_transitions': sum(totaux_index2_t.values())
        }

        print(f"      Précision règle Lupasco : {precision_lupasco:.4f}")
        print(f"      Chi² temporel : {chi2_temporel:.4f}, p-value : {p_value_temporel:.6f}")
        print(f"      V de Cramér temporel : {v_cramer_temporel:.4f}")
        print(f"      Information mutuelle temporelle : {mi_temporel:.4f} bits")

        return validation

    def _compter_transitions_temporelles(self, index1_data, index2_data):
        """Algorithme de comptage temporel selon formules optimales"""
        transitions = {}
        for t in range(len(index1_data) - 1):
            i2_t = index2_data[t]
            i1_t_plus_1 = index1_data[t + 1]

            if i2_t not in transitions:
                transitions[i2_t] = {}
            if i1_t_plus_1 not in transitions[i2_t]:
                transitions[i2_t][i1_t_plus_1] = 0

            transitions[i2_t][i1_t_plus_1] += 1

        return transitions

    def _calculer_precision_lupasco(self, index1_data, index2_data):
        """Algorithme de validation Lupasco selon formules optimales"""
        transitions_correctes = 0
        total_transitions = 0

        for t in range(len(index1_data) - 1):
            i1_t = index1_data[t]
            i2_t = index2_data[t]
            i1_t_plus_1 = index1_data[t + 1]

            # Règle Lupasco selon formules académiques
            if i2_t in ['pair_4', 'pair_6']:
                # Doit perpétuer
                if i1_t == i1_t_plus_1:
                    transitions_correctes += 1
            elif i2_t == 'impair_5':
                # Doit alterner
                if i1_t != i1_t_plus_1:
                    transitions_correctes += 1

            total_transitions += 1

        return transitions_correctes / total_transitions if total_transitions > 0 else 0

    def _tests_statistiques_temporels(self, transitions_temporelles):
        """Tests Chi² et V de Cramér temporels selon DataTab + Real Statistics"""
        try:
            # Construire matrice de contingence temporelle
            valeurs_i2 = sorted(transitions_temporelles.keys())
            valeurs_i1 = sorted(set(i1 for i2_dict in transitions_temporelles.values() for i1 in i2_dict.keys()))

            matrice_temporelle = []
            for i2 in valeurs_i2:
                ligne = []
                for i1 in valeurs_i1:
                    ligne.append(transitions_temporelles[i2].get(i1, 0))
                matrice_temporelle.append(ligne)

            # Vérifier que la matrice est valide pour Chi²
            matrice_array = np.array(matrice_temporelle)

            # Condition 1 : Aucune ligne ou colonne entièrement vide
            if np.any(np.sum(matrice_array, axis=0) == 0) or np.any(np.sum(matrice_array, axis=1) == 0):
                print("      ⚠️  Matrice temporelle avec lignes/colonnes vides - Chi² non applicable")
                return 0.0, 1.0, 0.0

            # Condition 2 : Au moins 80% des cellules avec effectif ≥ 5
            total_cellules = matrice_array.size
            cellules_valides = np.sum(matrice_array >= 5)
            if cellules_valides / total_cellules < 0.8:
                print("      ⚠️  Effectifs insuffisants pour Chi² - utilisation alternative")
                # Calcul Chi² avec correction de continuité
                chi2_stat = self._chi2_avec_correction(matrice_array)
                p_value = 1 - stats.chi2.cdf(chi2_stat, (len(valeurs_i1)-1)*(len(valeurs_i2)-1))
            else:
                # Test Chi² standard selon DataTab
                chi2_stat, p_value, dof, expected = stats.chi2_contingency(matrice_temporelle)

            # V de Cramér temporel selon Real Statistics
            n = np.sum(matrice_array)
            r = len(valeurs_i1)  # INDEX1
            c = len(valeurs_i2)  # INDEX2
            v_cramer = np.sqrt(chi2_stat / (n * min(r-1, c-1))) if n > 0 else 0.0

            return chi2_stat, p_value, v_cramer

        except Exception as e:
            print(f"      ⚠️  Erreur dans tests temporels : {str(e)}")
            return 0.0, 1.0, 0.0

    def _chi2_avec_correction(self, matrice):
        """Calcul Chi² avec correction pour petits effectifs"""
        # Calcul des effectifs attendus
        totaux_lignes = np.sum(matrice, axis=1)
        totaux_colonnes = np.sum(matrice, axis=0)
        total_general = np.sum(matrice)

        chi2 = 0
        for i in range(matrice.shape[0]):
            for j in range(matrice.shape[1]):
                attendu = (totaux_lignes[i] * totaux_colonnes[j]) / total_general
                if attendu > 0:
                    # Correction de continuité de Yates pour petits effectifs
                    observe = matrice[i, j]
                    correction = 0.5 if observe < attendu else -0.5
                    chi2 += ((observe + correction - attendu) ** 2) / attendu

        return chi2

    def _information_mutuelle_temporelle(self, index1_data, index2_data):
        """Information mutuelle temporelle selon Harvard"""
        # Créer paires temporelles
        paires_temporelles = [(index2_data[t], index1_data[t+1]) for t in range(len(index1_data)-1)]

        # Distributions
        from collections import Counter
        dist_i2_t = Counter([pair[0] for pair in paires_temporelles])
        dist_i1_t_plus_1 = Counter([pair[1] for pair in paires_temporelles])
        dist_jointe = Counter(paires_temporelles)

        n_total = len(paires_temporelles)

        # Calcul MI selon Harvard : MI = Σ P(x,y) × log₂[P(x,y)/(P(x)×P(y))]
        mi = 0
        for (i2, i1), count_joint in dist_jointe.items():
            p_joint = count_joint / n_total
            p_i2 = dist_i2_t[i2] / n_total
            p_i1 = dist_i1_t_plus_1[i1] / n_total

            if p_joint > 0 and p_i2 > 0 and p_i1 > 0:
                mi += p_joint * np.log2(p_joint / (p_i2 * p_i1))

        return mi

    def _verifier_contraintes_probabilites(self, probabilites_temporelles):
        """Vérifications mathématiques selon formules optimales"""
        verifications = {}

        # Contrainte : Σ P(INDEX1(t+1)|INDEX2(t)) = 1 pour chaque INDEX2(t)
        for i2_t, probas in probabilites_temporelles.items():
            somme = sum(probas.values())
            verifications[f"somme_P(INDEX1|{i2_t})"] = {
                'somme': somme,
                'valide': abs(somme - 1.0) < 1e-10
            }

        return verifications

    def _predire_index2_given_index1_synchrone(self, index1_data, index2_data):
        """
        Calcul de P(INDEX2(t+1)|INDEX1(t+1)) selon formules Stanford optimales
        """
        print("   Calcul P(INDEX2(t+1)|INDEX1(t+1)) selon formules académiques...")

        # Créer tableau de contingence synchrone selon Stanford
        from collections import defaultdict
        contingence_synchrone = defaultdict(lambda: defaultdict(int))

        for i1, i2 in zip(index1_data, index2_data):
            contingence_synchrone[i1][i2] += 1

        # Calculer probabilités conditionnelles selon Stanford
        # P(INDEX2(t+1)|INDEX1(t+1)) = count(INDEX2(t+1) ET INDEX1(t+1)) / count(INDEX1(t+1))
        probas_conditionnelles_synchrones = {}
        totaux_index1 = {}

        # Calculer totaux pour chaque INDEX1(t+1)
        for i1 in contingence_synchrone:
            totaux_index1[i1] = sum(contingence_synchrone[i1].values())

        # Appliquer formule Stanford
        for i1 in contingence_synchrone:
            probas_conditionnelles_synchrones[i1] = {}
            for i2 in contingence_synchrone[i1]:
                probas_conditionnelles_synchrones[i1][i2] = contingence_synchrone[i1][i2] / totaux_index1[i1]

        # Identification INDEX2_most_likely selon formules optimales
        # INDEX2_most_likely(i1) = argmax{P(pair_4|i1), P(pair_6|i1), P(impair_5|i1)}
        predictions_index2 = {}
        for i1 in probas_conditionnelles_synchrones:
            probas = probas_conditionnelles_synchrones[i1]
            most_likely = max(probas.items(), key=lambda x: x[1])
            predictions_index2[i1] = {
                'most_likely': most_likely[0],
                'probabilite': most_likely[1],
                'toutes_probas': probas
            }

        # Tests statistiques synchrones selon formules optimales
        chi2_synchrone, p_value_synchrone, v_cramer_synchrone = self._tests_statistiques_synchrones(contingence_synchrone)

        # Vérifications contraintes probabilités
        verifications_synchrones = self._verifier_contraintes_synchrones(probas_conditionnelles_synchrones)

        print(f"      Prédictions INDEX2 calculées pour {len(predictions_index2)} états INDEX1")
        print(f"      Chi² synchrone : {chi2_synchrone:.4f}, p-value : {p_value_synchrone:.6f}")
        print(f"      V de Cramér synchrone : {v_cramer_synchrone:.4f}")

        for i1, pred in predictions_index2.items():
            print(f"      Si INDEX1={i1} → INDEX2_most_likely={pred['most_likely']} (p={pred['probabilite']:.4f})")

        return {
            'probabilites_conditionnelles_synchrones': probas_conditionnelles_synchrones,
            'predictions_index2': predictions_index2,
            'contingence_synchrone': dict(contingence_synchrone),
            'chi2_synchrone': chi2_synchrone,
            'p_value_synchrone': p_value_synchrone,
            'v_cramer_synchrone': v_cramer_synchrone,
            'verifications_synchrones': verifications_synchrones
        }

    def _tests_statistiques_synchrones(self, contingence_synchrone):
        """Tests Chi² et V de Cramér synchrones selon Real Statistics"""
        try:
            # Construire matrice pour tests
            valeurs_i1 = sorted(contingence_synchrone.keys())
            valeurs_i2 = sorted(set(i2 for i1_dict in contingence_synchrone.values() for i2 in i1_dict.keys()))

            matrice_synchrone = []
            for i1 in valeurs_i1:
                ligne = []
                for i2 in valeurs_i2:
                    ligne.append(contingence_synchrone[i1].get(i2, 0))
                matrice_synchrone.append(ligne)

            # Vérifier validité pour Chi²
            matrice_array = np.array(matrice_synchrone)

            # Vérifier lignes/colonnes non vides
            if np.any(np.sum(matrice_array, axis=0) == 0) or np.any(np.sum(matrice_array, axis=1) == 0):
                print("      ⚠️  Matrice synchrone avec lignes/colonnes vides - Chi² non applicable")
                return 0.0, 1.0, 0.0

            # Test Chi² synchrone avec gestion d'erreurs
            try:
                chi2_stat, p_value, dof, expected = stats.chi2_contingency(matrice_synchrone)
            except ValueError:
                # Utiliser correction pour petits effectifs
                chi2_stat = self._chi2_avec_correction(matrice_array)
                p_value = 1 - stats.chi2.cdf(chi2_stat, (len(valeurs_i1)-1)*(len(valeurs_i2)-1))

            # V de Cramér synchrone selon Real Statistics
            n = np.sum(matrice_array)
            r = len(valeurs_i1)  # INDEX1
            c = len(valeurs_i2)  # INDEX2
            v_cramer = np.sqrt(chi2_stat / (n * min(r-1, c-1))) if n > 0 else 0.0

            return chi2_stat, p_value, v_cramer

        except Exception as e:
            print(f"      ⚠️  Erreur dans tests synchrones : {str(e)}")
            return 0.0, 1.0, 0.0

    def _verifier_contraintes_synchrones(self, probas_conditionnelles_synchrones):
        """Vérifications contraintes P(INDEX2|INDEX1)"""
        verifications = {}

        # Contrainte : Σ P(INDEX2(t+1)|INDEX1(t+1)) = 1 pour chaque INDEX1(t+1)
        for i1, probas in probas_conditionnelles_synchrones.items():
            somme = sum(probas.values())
            verifications[f"somme_P(INDEX2|{i1})"] = {
                'somme': somme,
                'valide': abs(somme - 1.0) < 1e-10
            }

        return verifications

    def _predire_index3_given_index1_index2(self, index1_data, index2_data, index3_data):
        """
        Prédiction finale INDEX3 selon formules Stanford optimales
        P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) avec métriques Paris-Saclay
        """
        print("   Calcul P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) selon formules académiques...")

        # Créer tableau de contingence 3D selon Stanford
        from collections import defaultdict
        contingence_3d = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_3d[i1][i2][i3] += 1

        # Calculer probabilités conditionnelles selon Stanford
        # P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) = count(INDEX3 ET INDEX1 ET INDEX2) / count(INDEX1 ET INDEX2)
        probas_conditionnelles_finales = {}
        predictions_index3 = {}

        for i1 in contingence_3d:
            probas_conditionnelles_finales[i1] = {}
            predictions_index3[i1] = {}

            for i2 in contingence_3d[i1]:
                total_i1_i2 = sum(contingence_3d[i1][i2].values())
                probas_conditionnelles_finales[i1][i2] = {}

                # Appliquer formule Stanford pour chaque INDEX3
                for i3 in contingence_3d[i1][i2]:
                    probas_conditionnelles_finales[i1][i2][i3] = contingence_3d[i1][i2][i3] / total_i1_i2

                # Identification INDEX3_most_likely selon formules optimales
                # INDEX3_most_likely(i1, i2) = argmax{P(PLAYER|i1,i2), P(BANKER|i1,i2), P(TIE|i1,i2)}
                if contingence_3d[i1][i2]:
                    probas_i3 = probas_conditionnelles_finales[i1][i2]
                    most_likely = max(probas_i3.items(), key=lambda x: x[1])
                    predictions_index3[i1][i2] = {
                        'most_likely': most_likely[0],
                        'probabilite': most_likely[1],
                        'toutes_probas': probas_i3
                    }

        # Métriques de performance selon Paris-Saclay
        metriques_performance = self._calculer_metriques_performance(
            index1_data, index2_data, index3_data, predictions_index3
        )

        # Matrice de confusion selon formules optimales
        matrice_confusion = self._calculer_matrice_confusion(
            index1_data, index2_data, index3_data, predictions_index3
        )

        # V de Cramér performance selon Real Statistics
        v_cramer_performance = self._calculer_v_cramer_performance(
            index1_data, index2_data, index3_data, predictions_index3
        )

        # Vérifications contraintes probabilités finales
        verifications_finales = self._verifier_contraintes_finales(probas_conditionnelles_finales)

        print(f"      Précision globale : {metriques_performance['precision_globale']:.4f}")
        print(f"      F1-Score moyen : {metriques_performance['f1_score_moyen']:.4f}")
        print(f"      V de Cramér performance : {v_cramer_performance:.4f}")
        print(f"      Prédictions correctes : {metriques_performance['predictions_correctes']:,}/{metriques_performance['total_predictions']:,}")

        # Afficher exemples de prédictions avec probabilités
        print("      Exemples de prédictions INDEX3 :")
        count = 0
        for i1 in predictions_index3:
            for i2 in predictions_index3[i1]:
                if count < 6:
                    pred = predictions_index3[i1][i2]
                    print(f"         Si INDEX1={i1}, INDEX2={i2} → INDEX3={pred['most_likely']} (p={pred['probabilite']:.4f})")
                    count += 1

        return {
            'probabilites_conditionnelles_finales': probas_conditionnelles_finales,
            'predictions_index3': predictions_index3,
            'metriques_performance': metriques_performance,
            'matrice_confusion': matrice_confusion,
            'v_cramer_performance': v_cramer_performance,
            'verifications_finales': verifications_finales,
            'contingence_3d': dict(contingence_3d)
        }

    def _calculer_metriques_performance(self, index1_data, index2_data, index3_data, predictions_index3):
        """Métriques de performance selon Paris-Saclay"""
        from collections import defaultdict

        # Compteurs pour métriques
        predictions_correctes = 0
        total_predictions = 0
        tp_par_classe = defaultdict(int)
        fp_par_classe = defaultdict(int)
        fn_par_classe = defaultdict(int)

        for i1, i2, i3_reel in zip(index1_data, index2_data, index3_data):
            if i1 in predictions_index3 and i2 in predictions_index3[i1]:
                prediction = predictions_index3[i1][i2]['most_likely']

                if prediction == i3_reel:
                    predictions_correctes += 1
                    tp_par_classe[i3_reel] += 1
                else:
                    fp_par_classe[prediction] += 1
                    fn_par_classe[i3_reel] += 1

                total_predictions += 1

        # Calculer métriques selon Paris-Saclay
        precision_globale = predictions_correctes / total_predictions if total_predictions > 0 else 0

        # Précision et rappel par classe
        precision_par_classe = {}
        rappel_par_classe = {}
        f1_par_classe = {}

        for classe in set(index3_data):
            tp = tp_par_classe[classe]
            fp = fp_par_classe[classe]
            fn = fn_par_classe[classe]

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            rappel = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * rappel) / (precision + rappel) if (precision + rappel) > 0 else 0

            precision_par_classe[classe] = precision
            rappel_par_classe[classe] = rappel
            f1_par_classe[classe] = f1

        # F1-Score moyen
        f1_score_moyen = sum(f1_par_classe.values()) / len(f1_par_classe) if f1_par_classe else 0

        return {
            'precision_globale': precision_globale,
            'predictions_correctes': predictions_correctes,
            'total_predictions': total_predictions,
            'precision_par_classe': precision_par_classe,
            'rappel_par_classe': rappel_par_classe,
            'f1_par_classe': f1_par_classe,
            'f1_score_moyen': f1_score_moyen
        }

    def _calculer_matrice_confusion(self, index1_data, index2_data, index3_data, predictions_index3):
        """Matrice de confusion selon formules optimales"""
        from collections import defaultdict

        classes = sorted(set(index3_data))
        matrice = defaultdict(lambda: defaultdict(int))

        for i1, i2, i3_reel in zip(index1_data, index2_data, index3_data):
            if i1 in predictions_index3 and i2 in predictions_index3[i1]:
                prediction = predictions_index3[i1][i2]['most_likely']
                matrice[i3_reel][prediction] += 1

        return dict(matrice)

    def _calculer_v_cramer_performance(self, index1_data, index2_data, index3_data, predictions_index3):
        """V de Cramér performance selon Real Statistics"""
        # Créer listes de prédictions vs réalité
        predictions_list = []
        realite_list = []

        for i1, i2, i3_reel in zip(index1_data, index2_data, index3_data):
            if i1 in predictions_index3 and i2 in predictions_index3[i1]:
                prediction = predictions_index3[i1][i2]['most_likely']
                predictions_list.append(prediction)
                realite_list.append(i3_reel)

        if not predictions_list:
            return 0.0

        # Créer matrice de contingence performance
        from collections import defaultdict
        contingence_perf = defaultdict(lambda: defaultdict(int))

        for pred, real in zip(predictions_list, realite_list):
            contingence_perf[real][pred] += 1

        # Construire matrice pour Chi²
        classes = sorted(set(predictions_list + realite_list))
        matrice_perf = []
        for real in classes:
            ligne = []
            for pred in classes:
                ligne.append(contingence_perf[real].get(pred, 0))
            matrice_perf.append(ligne)

        # Chi² et V de Cramér avec gestion d'erreurs
        if sum(sum(ligne) for ligne in matrice_perf) > 0:
            try:
                # Vérifier que la matrice est valide
                matrice_array = np.array(matrice_perf)
                if np.any(np.sum(matrice_array, axis=0) == 0) or np.any(np.sum(matrice_array, axis=1) == 0):
                    return 0.0

                chi2_stat, _, _, _ = stats.chi2_contingency(matrice_perf)
                n = sum(sum(ligne) for ligne in matrice_perf)
                k = len(classes)
                v_cramer = np.sqrt(chi2_stat / (n * (k - 1))) if k > 1 else 0.0
                return v_cramer
            except (ValueError, ZeroDivisionError):
                return 0.0

        return 0.0

    def _verifier_contraintes_finales(self, probas_conditionnelles_finales):
        """Vérifications contraintes P(INDEX3|INDEX1,INDEX2)"""
        verifications = {}

        # Contrainte : Σ P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) = 1
        for i1, dict_i2 in probas_conditionnelles_finales.items():
            for i2, probas in dict_i2.items():
                somme = sum(probas.values())
                verifications[f"somme_P(INDEX3|{i1},{i2})"] = {
                    'somme': somme,
                    'valide': abs(somme - 1.0) < 1e-10
                }

        return verifications

    def _analyser_index2_given_index1(self, index1_data, index2_data):
        """Analyse P(INDEX2|INDEX1)"""
        print("   Calcul des probabilités conditionnelles P(INDEX2|INDEX1)...")

        # Créer tableau de contingence
        from collections import defaultdict
        contingence = defaultdict(lambda: defaultdict(int))

        for i1, i2 in zip(index1_data, index2_data):
            contingence[i1][i2] += 1

        # Calculer probabilités conditionnelles
        probas_conditionnelles = {}
        totaux_index1 = {}

        # Calculer totaux pour chaque valeur d'INDEX1
        for i1 in contingence:
            totaux_index1[i1] = sum(contingence[i1].values())

        # Calculer P(INDEX2|INDEX1)
        for i1 in contingence:
            probas_conditionnelles[i1] = {}
            for i2 in contingence[i1]:
                probas_conditionnelles[i1][i2] = contingence[i1][i2] / totaux_index1[i1]

        # Créer matrice pour test chi2
        valeurs_i1 = sorted(contingence.keys())
        valeurs_i2 = sorted(set(i2 for i1_dict in contingence.values() for i2 in i1_dict.keys()))

        matrice_contingence = []
        for i1 in valeurs_i1:
            ligne = []
            for i2 in valeurs_i2:
                ligne.append(contingence[i1].get(i2, 0))
            matrice_contingence.append(ligne)

        # Test chi2 d'indépendance
        chi2_stat, p_value, dof, expected = stats.chi2_contingency(matrice_contingence)

        # V de Cramér
        n = sum(sum(ligne) for ligne in matrice_contingence)
        cramer_v = np.sqrt(chi2_stat / (n * (min(len(valeurs_i1), len(valeurs_i2)) - 1)))

        print(f"      Chi2 = {chi2_stat:.4f}, p-value = {p_value:.6f}")
        print(f"      V de Cramér = {cramer_v:.4f}")

        return {
            'probabilites_conditionnelles': probas_conditionnelles,
            'contingence': dict(contingence),
            'chi2_stat': chi2_stat,
            'p_value': p_value,
            'cramer_v': cramer_v,
            'valeurs_index1': valeurs_i1,
            'valeurs_index2': valeurs_i2,
            'matrice_contingence': matrice_contingence
        }

    def _analyser_index3_given_index1_index2(self, index1_data, index2_data, index3_data):
        """Analyse P(INDEX3|INDEX1, INDEX2)"""
        print("   Calcul des probabilités conditionnelles P(INDEX3|INDEX1, INDEX2)...")

        # Créer tableau de contingence 3D
        from collections import defaultdict
        contingence_3d = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_3d[i1][i2][i3] += 1

        # Calculer probabilités conditionnelles P(INDEX3|INDEX1, INDEX2)
        probas_conditionnelles = {}

        for i1 in contingence_3d:
            probas_conditionnelles[i1] = {}
            for i2 in contingence_3d[i1]:
                total_i1_i2 = sum(contingence_3d[i1][i2].values())
                probas_conditionnelles[i1][i2] = {}
                for i3 in contingence_3d[i1][i2]:
                    probas_conditionnelles[i1][i2][i3] = contingence_3d[i1][i2][i3] / total_i1_i2

        # Tests chi2 pour chaque combinaison INDEX1-INDEX2
        tests_chi2 = {}

        for i1 in contingence_3d:
            for i2 in contingence_3d[i1]:
                if len(contingence_3d[i1][i2]) > 1:  # Au moins 2 valeurs d'INDEX3
                    # Test si INDEX3 suit distribution uniforme pour cette combinaison
                    observes = list(contingence_3d[i1][i2].values())
                    total = sum(observes)
                    attendus = [total / len(observes)] * len(observes)

                    if all(a >= 5 for a in attendus):  # Condition pour chi2
                        chi2_stat = sum((o - a)**2 / a for o, a in zip(observes, attendus))
                        p_value = 1 - stats.chi2.cdf(chi2_stat, len(observes) - 1)
                        tests_chi2[f"{i1}_{i2}"] = {
                            'chi2_stat': chi2_stat,
                            'p_value': p_value,
                            'observes': observes,
                            'attendus': attendus
                        }

        print(f"      Calculé {len(tests_chi2)} tests chi2 pour combinaisons INDEX1-INDEX2")

        return {
            'probabilites_conditionnelles': probas_conditionnelles,
            'contingence_3d': dict(contingence_3d),
            'tests_chi2': tests_chi2
        }

    def _analyser_influences_causales(self, index1_data, index2_data, index3_data):
        """Analyse des influences causales entre les INDEX"""
        print("   Calcul des mesures d'influence causale...")

        # Distributions marginales
        dist_i1 = Counter(index1_data)
        dist_i2 = Counter(index2_data)
        dist_i3 = Counter(index3_data)

        n_total = len(index1_data)

        # Probabilités marginales
        prob_i1 = {k: v/n_total for k, v in dist_i1.items()}
        prob_i2 = {k: v/n_total for k, v in dist_i2.items()}
        prob_i3 = {k: v/n_total for k, v in dist_i3.items()}

        influences = {}

        # 1. Influence INDEX1 → INDEX2
        print("      Analyse influence INDEX1 → INDEX2...")
        contingence_i1_i2 = defaultdict(lambda: defaultdict(int))
        for i1, i2 in zip(index1_data, index2_data):
            contingence_i1_i2[i1][i2] += 1

        # Calculer écarts par rapport à l'indépendance
        ecarts_i1_i2 = {}
        for i1 in contingence_i1_i2:
            total_i1 = sum(contingence_i1_i2[i1].values())
            ecarts_i1_i2[i1] = {}
            for i2 in contingence_i1_i2[i1]:
                prob_conditionnelle = contingence_i1_i2[i1][i2] / total_i1
                prob_marginale = prob_i2.get(i2, 0)
                ecart = abs(prob_conditionnelle - prob_marginale)
                ecarts_i1_i2[i1][i2] = {
                    'prob_conditionnelle': prob_conditionnelle,
                    'prob_marginale': prob_marginale,
                    'ecart': ecart
                }

        influences['index1_vers_index2'] = ecarts_i1_i2

        # 2. Influence INDEX2 → INDEX3
        print("      Analyse influence INDEX2 → INDEX3...")
        contingence_i2_i3 = defaultdict(lambda: defaultdict(int))
        for i2, i3 in zip(index2_data, index3_data):
            contingence_i2_i3[i2][i3] += 1

        ecarts_i2_i3 = {}
        for i2 in contingence_i2_i3:
            total_i2 = sum(contingence_i2_i3[i2].values())
            ecarts_i2_i3[i2] = {}
            for i3 in contingence_i2_i3[i2]:
                prob_conditionnelle = contingence_i2_i3[i2][i3] / total_i2
                prob_marginale = prob_i3.get(i3, 0)
                ecart = abs(prob_conditionnelle - prob_marginale)
                ecarts_i2_i3[i2][i3] = {
                    'prob_conditionnelle': prob_conditionnelle,
                    'prob_marginale': prob_marginale,
                    'ecart': ecart
                }

        influences['index2_vers_index3'] = ecarts_i2_i3

        # 3. Influence INDEX1 → INDEX3 (direct)
        print("      Analyse influence INDEX1 → INDEX3 (direct)...")
        contingence_i1_i3 = defaultdict(lambda: defaultdict(int))
        for i1, i3 in zip(index1_data, index3_data):
            contingence_i1_i3[i1][i3] += 1

        ecarts_i1_i3 = {}
        for i1 in contingence_i1_i3:
            total_i1 = sum(contingence_i1_i3[i1].values())
            ecarts_i1_i3[i1] = {}
            for i3 in contingence_i1_i3[i1]:
                prob_conditionnelle = contingence_i1_i3[i1][i3] / total_i1
                prob_marginale = prob_i3.get(i3, 0)
                ecart = abs(prob_conditionnelle - prob_marginale)
                ecarts_i1_i3[i1][i3] = {
                    'prob_conditionnelle': prob_conditionnelle,
                    'prob_marginale': prob_marginale,
                    'ecart': ecart
                }

        influences['index1_vers_index3_direct'] = ecarts_i1_i3

        # 4. Information mutuelle
        print("      Calcul information mutuelle...")

        # Information mutuelle INDEX1-INDEX2
        mi_i1_i2 = 0
        for i1 in contingence_i1_i2:
            for i2 in contingence_i1_i2[i1]:
                p_joint = contingence_i1_i2[i1][i2] / n_total
                p_i1 = prob_i1[i1]
                p_i2 = prob_i2[i2]
                if p_joint > 0 and p_i1 > 0 and p_i2 > 0:
                    mi_i1_i2 += p_joint * np.log2(p_joint / (p_i1 * p_i2))

        # Information mutuelle INDEX2-INDEX3
        mi_i2_i3 = 0
        for i2 in contingence_i2_i3:
            for i3 in contingence_i2_i3[i2]:
                p_joint = contingence_i2_i3[i2][i3] / n_total
                p_i2 = prob_i2[i2]
                p_i3 = prob_i3[i3]
                if p_joint > 0 and p_i2 > 0 and p_i3 > 0:
                    mi_i2_i3 += p_joint * np.log2(p_joint / (p_i2 * p_i3))

        influences['information_mutuelle'] = {
            'index1_index2': mi_i1_i2,
            'index2_index3': mi_i2_i3
        }

        return influences

    def _analyser_capacites_predictives(self, index1_data, index2_data, index3_data):
        """Analyse des capacités prédictives du système"""
        print("   Calcul des capacités prédictives...")

        capacites = {}

        # 1. Prédiction INDEX2 à partir d'INDEX1
        print("      Prédiction INDEX2 à partir d'INDEX1...")

        # Calculer argmax(P(INDEX2|INDEX1))
        contingence_i1_i2 = defaultdict(lambda: defaultdict(int))
        for i1, i2 in zip(index1_data, index2_data):
            contingence_i1_i2[i1][i2] += 1

        predictions_i2 = {}
        for i1 in contingence_i1_i2:
            total_i1 = sum(contingence_i1_i2[i1].values())
            probas = {i2: count/total_i1 for i2, count in contingence_i1_i2[i1].items()}
            prediction = max(probas.items(), key=lambda x: x[1])
            predictions_i2[i1] = {
                'prediction': prediction[0],
                'probabilite': prediction[1],
                'toutes_probas': probas
            }

        # Calculer précision de prédiction
        predictions_correctes_i2 = 0
        total_predictions_i2 = 0

        for i1, i2_reel in zip(index1_data, index2_data):
            if i1 in predictions_i2:
                prediction = predictions_i2[i1]['prediction']
                if prediction == i2_reel:
                    predictions_correctes_i2 += 1
                total_predictions_i2 += 1

        precision_i2 = predictions_correctes_i2 / total_predictions_i2 if total_predictions_i2 > 0 else 0

        capacites['prediction_index2'] = {
            'predictions': predictions_i2,
            'precision': precision_i2,
            'predictions_correctes': predictions_correctes_i2,
            'total_predictions': total_predictions_i2
        }

        # 2. Prédiction INDEX3 à partir d'(INDEX1, INDEX2)
        print("      Prédiction INDEX3 à partir d'(INDEX1, INDEX2)...")

        contingence_i1_i2_i3 = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_i1_i2_i3[i1][i2][i3] += 1

        predictions_i3 = {}
        for i1 in contingence_i1_i2_i3:
            predictions_i3[i1] = {}
            for i2 in contingence_i1_i2_i3[i1]:
                total_i1_i2 = sum(contingence_i1_i2_i3[i1][i2].values())
                if total_i1_i2 > 0:
                    probas = {i3: count/total_i1_i2 for i3, count in contingence_i1_i2_i3[i1][i2].items()}
                    prediction = max(probas.items(), key=lambda x: x[1])
                    predictions_i3[i1][i2] = {
                        'prediction': prediction[0],
                        'probabilite': prediction[1],
                        'toutes_probas': probas
                    }

        # Calculer précision de prédiction INDEX3
        predictions_correctes_i3 = 0
        total_predictions_i3 = 0

        for i1, i2, i3_reel in zip(index1_data, index2_data, index3_data):
            if i1 in predictions_i3 and i2 in predictions_i3[i1]:
                prediction = predictions_i3[i1][i2]['prediction']
                if prediction == i3_reel:
                    predictions_correctes_i3 += 1
                total_predictions_i3 += 1

        precision_i3 = predictions_correctes_i3 / total_predictions_i3 if total_predictions_i3 > 0 else 0

        capacites['prediction_index3'] = {
            'predictions': predictions_i3,
            'precision': precision_i3,
            'predictions_correctes': predictions_correctes_i3,
            'total_predictions': total_predictions_i3
        }

        print(f"      Précision prédiction INDEX2 : {precision_i2:.4f}")
        print(f"      Précision prédiction INDEX3 : {precision_i3:.4f}")

        return capacites

    def analyser_toutes_sequences(self):
        """Lance l'analyse complète de toutes les séquences"""
        print("\nLANCEMENT DE L'ANALYSE COMPLÈTE")
        print("=" * 50)

        # 1. Analyse des runs (analyse originale)
        print("\nPHASE 1: ANALYSE DES RUNS")
        print("-" * 30)

        for nom_index, sequence in self.sequences.items():
            if nom_index in ['INDEX1', 'INDEX2', 'INDEX3']:  # Analyser seulement les INDEX principaux
                print(f"\nAnalyse des runs {nom_index}")

                # Analyse des runs
                resultats_runs = self.analyser_runs(sequence, nom_index)

                # Autocorrélation
                print(f"Calcul de l'autocorrélation...")
                autocorr = self.calculer_autocorrelation(sequence)

                # Entropie de Shannon
                print(f"Calcul de l'entropie de Shannon...")
                entropie = self.calculer_entropie_shannon(sequence)

                # Stocker les résultats
                self.resultats[nom_index] = {
                    'runs': resultats_runs,
                    'autocorrelation': autocorr,
                    'entropie_shannon': entropie,
                    'taille_sequence': len(sequence)
                }

                print(f"Analyse {nom_index} terminée")

        # 2. Analyse des probabilités conditionnelles (nouvelle analyse)
        print("\nPHASE 2: ANALYSE DES PROBABILITÉS CONDITIONNELLES")
        print("-" * 50)

        resultats_probas = self.analyser_probabilites_conditionnelles()
        self.resultats['probabilites_conditionnelles'] = resultats_probas

        # 3. Analyse prédictive Lupasco optimisée (formules académiques)
        print("\nPHASE 3: ANALYSE PRÉDICTIVE LUPASCO OPTIMISÉE")
        print("-" * 50)

        resultats_predictions = self.analyser_predictions_lupasco()
        self.resultats['predictions_lupasco'] = resultats_predictions

        # 4. Génération rapport prédictions optimales
        print("\nPHASE 4: GÉNÉRATION RAPPORT PRÉDICTIONS OPTIMALES")
        print("-" * 50)

        rapport_predictions = self.generer_rapport_predictions_optimales(resultats_predictions)

        # 5. Tests de prédiction cloisonnée en temps réel
        print("\nPHASE 5: TESTS PRÉDICTIONS CLOISONNÉES TEMPS RÉEL")
        print("-" * 55)

        for index1_test in ['SYNC', 'DESYNC']:
            prediction = self.predire_main_suivante_cloisonnee(index1_test, resultats_predictions)

        print("\nAnalyse complète terminée avec cloisonnement temporel strict")

    def generer_rapport(self, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des analyses

        Args:
            fichier_sortie: Nom du fichier de rapport (optionnel)
        """
        if not fichier_sortie:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fichier_sortie = f"rapport_analyse_sequences_{timestamp}.txt"

        print(f"\n📝 Génération du rapport : {fichier_sortie}")

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("RAPPORT D'ANALYSE STATISTIQUE DES SÉQUENCES LUPASCO\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date d'analyse : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Fichier source : {self.fichier_json}\n")
            f.write(f"Nombre de parties : {self.nb_parties_total:,}\n\n")

            # Rapport pour chaque index
            for nom_index, resultats in self.resultats.items():
                if nom_index == 'probabilites_conditionnelles':
                    continue  # Traité séparément plus bas

                f.write(f"\n{'='*20} ANALYSE {nom_index} {'='*20}\n\n")
                f.write(f"Taille de la séquence : {resultats['taille_sequence']:,} éléments\n")
                f.write(f"Entropie de Shannon : {resultats['entropie_shannon']:.4f} bits\n\n")

                # Analyse des runs par valeur
                f.write("ANALYSE DES RUNS PAR VALEUR :\n")
                f.write("-" * 40 + "\n")

                for valeur, stats in resultats['runs'].items():
                    if valeur == 'global':
                        continue

                    f.write(f"\n🎯 {valeur} :\n")

                    # Statistiques descriptives
                    stats_desc = stats['statistiques']
                    f.write(f"   Nombre de runs : {stats_desc['nombre_runs']:,}\n")
                    f.write(f"   Longueur moyenne : {stats_desc['longueur_moyenne']:.2f}\n")
                    f.write(f"   Longueur médiane : {stats_desc['longueur_mediane']:.2f}\n")
                    f.write(f"   Longueur max : {stats_desc['longueur_max']}\n")
                    f.write(f"   Écart-type : {stats_desc['ecart_type']:.2f}\n")

                    # Comparaison théorique
                    theorique = stats['theorique']
                    f.write(f"   Probabilité : {theorique['probabilite']:.4f}\n")
                    f.write(f"   Longueur moyenne théorique : {theorique['longueur_moyenne_theorique']:.2f}\n")

                    # Écarts
                    ecarts = stats['ecarts']
                    f.write(f"   Écart longueur moyenne : {ecarts['ecart_longueur_moyenne']:.2f}\n")

                    # Tests statistiques
                    if 'ks_test' in stats['tests'] and stats['tests']['ks_test']['p_value']:
                        ks = stats['tests']['ks_test']
                        f.write(f"   Test KS p-value : {ks['p_value']:.4f}\n")
                        f.write(f"   Test KS significatif : {'Oui' if ks['p_value'] < 0.05 else 'Non'}\n")

                    # Distribution complète des longueurs
                    f.write("   Distribution complète des longueurs :\n")
                    dist_sorted = sorted(stats_desc['distribution'].items(), key=lambda x: x[0])  # Tri par longueur
                    for longueur, count in dist_sorted:
                        f.write(f"     Longueur {longueur} : {count:,} fois\n")

                # Analyse globale
                if 'global' in resultats['runs']:
                    global_stats = resultats['runs']['global']
                    f.write(f"\nANALYSE GLOBALE :\n")
                    f.write("-" * 20 + "\n")
                    f.write(f"Nombre total de runs : {global_stats['nombre_total_runs']:,}\n")
                    f.write(f"Longueur moyenne globale : {global_stats['longueur_moyenne_globale']:.2f}\n")
                    f.write(f"Longueur max globale : {global_stats['longueur_max_globale']}\n")

                    # Runs test
                    if 'runs_test' in global_stats:
                        rt = global_stats['runs_test']
                        f.write(f"\nRUNS TEST (Test de randomness) :\n")
                        f.write(f"   Runs observés : {rt['runs_observes']}\n")
                        f.write(f"   Runs attendus : {rt['runs_attendus']:.2f}\n")
                        f.write(f"   Z-score : {rt['z_score']:.4f}\n")
                        f.write(f"   P-value : {rt['p_value']:.4f}\n")
                        f.write(f"   Significatif : {'Oui' if rt['significatif'] else 'Non'}\n")

                # Autocorrélation
                f.write(f"\nAUTOCORRÉLATION :\n")
                f.write("-" * 15 + "\n")
                autocorr = resultats['autocorrelation']
                for lag in sorted(autocorr.keys())[:10]:  # Premiers 10 lags
                    f.write(f"   Lag {lag} : {autocorr[lag]:.4f}\n")

            # Rapport des probabilités conditionnelles
            if 'probabilites_conditionnelles' in self.resultats:
                f.write(f"\n{'='*60}\n")
                f.write("ANALYSE DES PROBABILITÉS CONDITIONNELLES\n")
                f.write("="*60 + "\n\n")

                probas_results = self.resultats['probabilites_conditionnelles']

                # INDEX2 en fonction d'INDEX1
                if 'index2_given_index1' in probas_results:
                    f.write("1. ANALYSE INDEX2 en fonction d'INDEX1\n")
                    f.write("-" * 40 + "\n")

                    i2_i1 = probas_results['index2_given_index1']
                    f.write(f"Test d'indépendance Chi² = {i2_i1['chi2_stat']:.4f}\n")
                    f.write(f"P-value = {i2_i1['p_value']:.6f}\n")
                    f.write(f"V de Cramér = {i2_i1['cramer_v']:.4f}\n")
                    f.write(f"Indépendance : {'Rejetée' if i2_i1['p_value'] < 0.05 else 'Acceptée'}\n\n")

                    f.write("Probabilités conditionnelles P(INDEX2|INDEX1) :\n")
                    for i1, probas in i2_i1['probabilites_conditionnelles'].items():
                        f.write(f"   {i1} :\n")
                        for i2, proba in probas.items():
                            f.write(f"      P({i2}|{i1}) = {proba:.4f}\n")
                        f.write("\n")

                # INDEX3 en fonction d'(INDEX1, INDEX2)
                if 'index3_given_index1_index2' in probas_results:
                    f.write("2. ANALYSE INDEX3 en fonction d'(INDEX1, INDEX2)\n")
                    f.write("-" * 45 + "\n")

                    i3_i1_i2 = probas_results['index3_given_index1_index2']
                    f.write("Probabilités conditionnelles P(INDEX3|INDEX1, INDEX2) :\n")

                    for i1, dict_i2 in i3_i1_i2['probabilites_conditionnelles'].items():
                        f.write(f"   {i1} :\n")
                        for i2, probas_i3 in dict_i2.items():
                            f.write(f"      {i2} :\n")
                            for i3, proba in probas_i3.items():
                                f.write(f"         P({i3}|{i1},{i2}) = {proba:.4f}\n")
                        f.write("\n")

                # Influences causales
                if 'influences_causales' in probas_results:
                    f.write("3. MESURES D'INFLUENCE CAUSALE\n")
                    f.write("-" * 30 + "\n")

                    influences = probas_results['influences_causales']

                    if 'information_mutuelle' in influences:
                        mi = influences['information_mutuelle']
                        f.write("Information mutuelle :\n")
                        f.write(f"   I(INDEX1; INDEX2) = {mi['index1_index2']:.4f} bits\n")
                        f.write(f"   I(INDEX2; INDEX3) = {mi['index2_index3']:.4f} bits\n\n")

                # Capacités prédictives
                if 'analyses_predictives' in probas_results:
                    f.write("4. CAPACITÉS PRÉDICTIVES\n")
                    f.write("-" * 25 + "\n")

                    pred = probas_results['analyses_predictives']

                    if 'prediction_index2' in pred:
                        p_i2 = pred['prediction_index2']
                        f.write(f"Prédiction INDEX2 à partir d'INDEX1 :\n")
                        f.write(f"   Précision = {p_i2['precision']:.4f}\n")
                        f.write(f"   Prédictions correctes = {p_i2['predictions_correctes']:,}\n")
                        f.write(f"   Total prédictions = {p_i2['total_predictions']:,}\n\n")

                    if 'prediction_index3' in pred:
                        p_i3 = pred['prediction_index3']
                        f.write(f"Prédiction INDEX3 à partir d'(INDEX1, INDEX2) :\n")
                        f.write(f"   Précision = {p_i3['precision']:.4f}\n")
                        f.write(f"   Prédictions correctes = {p_i3['predictions_correctes']:,}\n")
                        f.write(f"   Total prédictions = {p_i3['total_predictions']:,}\n\n")

            # Rapport des prédictions Lupasco
            if 'predictions_lupasco' in self.resultats:
                f.write(f"\n{'='*60}\n")
                f.write("ANALYSE PRÉDICTIVE LUPASCO\n")
                f.write("="*60 + "\n\n")

                pred_results = self.resultats['predictions_lupasco']

                # 1. Validation de la règle Lupasco
                if 'validation_lupasco' in pred_results:
                    f.write("1. VALIDATION DE LA RÈGLE LUPASCO\n")
                    f.write("-" * 35 + "\n")

                    validation = pred_results['validation_lupasco']
                    f.write(f"Précision globale de la règle : {validation['precision_globale']:.4f}\n")
                    f.write(f"Transitions correctes : {validation['transitions_correctes']:,}\n")
                    f.write(f"Transitions incorrectes : {validation['transitions_incorrectes']:,}\n")
                    f.write(f"Total transitions : {validation['total_transitions']:,}\n\n")

                    f.write("Détails par INDEX2 :\n")
                    for index2_val, details in validation['details_par_index2'].items():
                        f.write(f"   {index2_val} :\n")
                        f.write(f"      Précision : {details['precision']:.4f}\n")
                        f.write(f"      Perpétue : {details['perpetue']:,} ({details['prob_perpetue']:.4f})\n")
                        f.write(f"      Alterne : {details['alterne']:,} ({details['prob_alterne']:.4f})\n")
                        f.write(f"      Total : {details['total']:,}\n\n")

                    f.write("Probabilités conditionnelles P(INDEX1(t+1)|INDEX2(t)) :\n")
                    for index2_val, probas in validation['probabilites_conditionnelles'].items():
                        f.write(f"   {index2_val} :\n")
                        for index1_val, proba in probas.items():
                            f.write(f"      P(INDEX1(t+1)={index1_val}|INDEX2(t)={index2_val}) = {proba:.4f}\n")
                        f.write("\n")

                # 2. Prédictions INDEX2
                if 'prediction_index2' in pred_results:
                    f.write("2. PRÉDICTIONS INDEX2 SYNCHRONES\n")
                    f.write("-" * 30 + "\n")

                    pred_i2 = pred_results['prediction_index2']
                    f.write("Prédictions INDEX2_most_likely :\n")
                    for i1, pred in pred_i2['predictions'].items():
                        f.write(f"   Si INDEX1={i1} → INDEX2_most_likely={pred['most_likely']} (p={pred['probabilite']:.4f})\n")
                    f.write("\n")

                # 3. Prédictions INDEX3
                if 'prediction_index3' in pred_results:
                    f.write("3. PRÉDICTIONS FINALES INDEX3\n")
                    f.write("-" * 25 + "\n")

                    pred_i3 = pred_results['prediction_index3']
                    f.write(f"Précision prédictive globale : {pred_i3['precision_globale']:.4f}\n")
                    f.write(f"Prédictions correctes : {pred_i3['predictions_correctes']:,}\n")
                    f.write(f"Total prédictions : {pred_i3['total_predictions']:,}\n\n")

                    f.write("Exemples de prédictions INDEX3 :\n")
                    count = 0
                    for i1 in pred_i3['predictions']:
                        for i2 in pred_i3['predictions'][i1]:
                            if count < 12:  # Afficher 12 exemples dans le rapport
                                pred = pred_i3['predictions'][i1][i2]
                                f.write(f"   Si INDEX1={i1}, INDEX2={i2} → INDEX3={pred['most_likely']} (p={pred['probabilite']:.4f})\n")
                                count += 1
                    f.write("\n")

        print(f"Rapport généré : {fichier_sortie}")
        return fichier_sortie

    def generer_graphiques(self, dossier_sortie: str = "graphiques_sequences"):
        """
        Génère des graphiques d'analyse

        Args:
            dossier_sortie: Dossier pour sauvegarder les graphiques
        """
        if not HAS_MATPLOTLIB:
            print("⚠️  Matplotlib non disponible, graphiques non générés")
            return

        import os

        if not os.path.exists(dossier_sortie):
            os.makedirs(dossier_sortie)

        print(f"\n📊 Génération des graphiques dans : {dossier_sortie}")

        for nom_index, resultats in self.resultats.items():
            # Ignorer les résultats qui ne sont pas des analyses de runs
            if nom_index == 'probabilites_conditionnelles' or 'runs' not in resultats:
                continue

            # Graphique des distributions de longueurs de runs
            plt.figure(figsize=(12, 8))

            subplot_idx = 1
            valeurs = [v for v in resultats['runs'].keys() if v != 'global']
            n_valeurs = len(valeurs)

            for valeur in valeurs:
                plt.subplot(2, (n_valeurs + 1) // 2, subplot_idx)

                stats_desc = resultats['runs'][valeur]['statistiques']
                distribution = stats_desc['distribution']

                longueurs = list(distribution.keys())
                frequences = list(distribution.values())

                plt.bar(longueurs, frequences, alpha=0.7)
                plt.title(f'Distribution runs {valeur}')
                plt.xlabel('Longueur du run')
                plt.ylabel('Fréquence')
                plt.grid(True, alpha=0.3)

                subplot_idx += 1

            plt.tight_layout()
            plt.savefig(f"{dossier_sortie}/distribution_runs_{nom_index}.png", dpi=300, bbox_inches='tight')
            plt.close()

            # Graphique d'autocorrélation
            if 'autocorrelation' in resultats:
                plt.figure(figsize=(10, 6))
                autocorr = resultats['autocorrelation']
                lags = list(autocorr.keys())
                correlations = list(autocorr.values())

                plt.plot(lags, correlations, 'bo-', markersize=4)
                plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
                plt.title(f'Autocorrélation - {nom_index}')
                plt.xlabel('Lag')
                plt.ylabel('Coefficient d\'autocorrélation')
                plt.grid(True, alpha=0.3)

                plt.savefig(f"{dossier_sortie}/autocorrelation_{nom_index}.png", dpi=300, bbox_inches='tight')
                plt.close()

        print(f"✅ Graphiques générés dans {dossier_sortie}/")


def main():
    """Fonction principale du programme"""
    import sys

    print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
    print("=" * 60)
    print("Basé sur : recherche_analyse_statistique_sequences.txt")
    print()

    # Vérifier les arguments
    if len(sys.argv) < 2:
        print("❌ Erreur : Fichier JSON requis")
        print("\nUsage:")
        print("  python analyseur_sequences_lupasco.py <fichier_json>")
        print("\nExemple:")
        print("  python analyseur_sequences_lupasco.py dataset_baccarat_lupasco_20250617_232800.json")
        sys.exit(1)

    fichier_json = sys.argv[1]

    try:
        # Créer l'analyseur
        analyseur = AnalyseurSequencesLupasco(fichier_json)

        # Charger les données
        analyseur.charger_donnees()

        # Lancer l'analyse complète
        analyseur.analyser_toutes_sequences()

        # Générer le rapport
        fichier_rapport = analyseur.generer_rapport()

        # Générer les graphiques
        try:
            analyseur.generer_graphiques()
        except ImportError:
            print("⚠️  Matplotlib non disponible, graphiques non générés")
        except Exception as e:
            print(f"⚠️  Erreur lors de la génération des graphiques : {e}")

        print(f"\n🎉 ANALYSE TERMINÉE AVEC SUCCÈS !")
        print(f"📝 Rapport détaillé : {fichier_rapport}")
        print(f"📊 Graphiques : dossier graphiques_sequences/")

    except Exception as e:
        print(f"\n❌ ERREUR : {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
