#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ANALYSEUR STATISTIQUE DES SÉQUENCES CONSÉCUTIVES - SYSTÈME LUPASCO
===================================================================

Programme d'analyse statistique des séquences consécutives basé sur la recherche
académique pour le dataset Baccarat Lupasco de 100 000 parties.

Analyse les runs/séquences de :
- SYNC/DESYNC (INDEX1)
- pair_4/pair_6/impair_5 (INDEX2)  
- PLAYER/BANKER (INDEX3)

Basé sur : recherche_analyse_statistique_sequences.txt
"""

import json
import numpy as np
from scipy import stats
from scipy.stats import chi2, kstest, geom
from collections import Counter, defaultdict
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Imports optionnels
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences consécutives pour le système Lupasco
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec le fichier JSON
        
        Args:
            fichier_json: Chemin vers le fichier JSON du dataset Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        
        print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
        print("=" * 60)
        print(f"📂 Fichier à analyser : {fichier_json}")
        
    def charger_donnees(self):
        """Charge et extrait les données du fichier JSON"""
        print("\n📊 Chargement des données...")
        
        try:
            with open(self.fichier_json, 'r', encoding='utf-8') as f:
                self.donnees = json.load(f)
            
            nb_parties = len(self.donnees['parties'])
            print(f"✅ {nb_parties:,} parties chargées")
            
            # Extraire les séquences de toutes les parties
            self._extraire_sequences()
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise
    
    def _extraire_sequences(self):
        """Extrait les séquences de tous les index pour toutes les parties"""
        print("🔍 Extraction des séquences...")
        
        # Initialiser les listes de séquences
        index1_seq = []  # SYNC/DESYNC
        index2_seq = []  # pair_4/pair_6/impair_5
        index3_seq = []  # PLAYER/BANKER (exclure TIE pour analyse binaire)
        
        total_mains = 0
        
        for partie in self.donnees['parties']:
            for main in partie['mains']:
                # INDEX1 : SYNC/DESYNC
                index1_seq.append(main['index1_sync_state'])
                
                # INDEX2 : pair_4/pair_6/impair_5
                index2_seq.append(main['index2_cards_category'])
                
                # INDEX3 : PLAYER/BANKER (exclure TIE pour runs binaires)
                if main['index3_result'] in ['PLAYER', 'BANKER']:
                    index3_seq.append(main['index3_result'])
                
                total_mains += 1
        
        self.sequences = {
            'INDEX1': index1_seq,
            'INDEX2': index2_seq,
            'INDEX3': index3_seq
        }
        
        print(f"✅ {total_mains:,} mains extraites")
        print(f"   - INDEX1 (SYNC/DESYNC) : {len(index1_seq):,} valeurs")
        print(f"   - INDEX2 (pair_4/6/impair_5) : {len(index2_seq):,} valeurs")
        print(f"   - INDEX3 (PLAYER/BANKER) : {len(index3_seq):,} valeurs")
    
    def analyser_runs(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse les runs (séquences consécutives) d'une séquence
        
        Args:
            sequence: Liste des valeurs de la séquence
            nom_sequence: Nom de la séquence pour l'affichage
            
        Returns:
            dict: Résultats de l'analyse des runs
        """
        print(f"\n🔬 Analyse des runs : {nom_sequence}")
        
        # Identifier les runs
        runs = []
        if not sequence:
            return {}
        
        current_value = sequence[0]
        current_length = 1
        
        for i in range(1, len(sequence)):
            if sequence[i] == current_value:
                current_length += 1
            else:
                runs.append((current_value, current_length))
                current_value = sequence[i]
                current_length = 1
        
        # Ajouter le dernier run
        runs.append((current_value, current_length))
        
        # Analyser par type de valeur
        resultats = {}
        valeurs_uniques = list(set(sequence))
        
        for valeur in valeurs_uniques:
            longueurs_runs = [length for value, length in runs if value == valeur]
            
            if longueurs_runs:
                resultats[valeur] = self._analyser_runs_valeur(
                    longueurs_runs, valeur, len(sequence), sequence.count(valeur)
                )
        
        # Statistiques globales
        resultats['global'] = self._analyser_runs_global(runs, sequence)
        
        return resultats
    
    def _analyser_runs_valeur(self, longueurs: list, valeur: str, n_total: int, n_valeur: int) -> dict:
        """Analyse les runs pour une valeur spécifique"""
        
        # Statistiques descriptives
        stats_desc = {
            'nombre_runs': len(longueurs),
            'longueur_moyenne': np.mean(longueurs),
            'longueur_mediane': np.median(longueurs),
            'longueur_max': max(longueurs),
            'longueur_min': min(longueurs),
            'ecart_type': np.std(longueurs),
            'distribution': Counter(longueurs)
        }
        
        # Probabilité théorique
        p = n_valeur / n_total
        
        # Longueur moyenne théorique (distribution géométrique)
        longueur_moyenne_theorique = 1 / p if p > 0 else float('inf')
        
        # Nombre de runs théorique
        # Formule : E[R] ≈ 2np(1-p) + 1 pour le nombre total de runs
        # Pour une valeur spécifique : approximativement n_valeur / longueur_moyenne_theorique
        nombre_runs_theorique = n_valeur / longueur_moyenne_theorique if longueur_moyenne_theorique != float('inf') else 0
        
        # Tests statistiques
        tests = {}
        
        # Test de Kolmogorov-Smirnov contre distribution géométrique
        if p > 0 and p < 1:
            # Distribution géométrique théorique
            longueurs_max = max(longueurs)
            x_theorique = np.arange(1, longueurs_max + 1)
            cdf_theorique = geom.cdf(x_theorique, p)
            
            # CDF empirique
            longueurs_sorted = np.sort(longueurs)
            cdf_empirique = np.arange(1, len(longueurs) + 1) / len(longueurs)
            
            # Test KS (approximation)
            try:
                ks_stat, ks_pvalue = kstest(longueurs, lambda x: geom.cdf(x, p))
                tests['ks_test'] = {'statistic': ks_stat, 'p_value': ks_pvalue}
            except:
                tests['ks_test'] = {'statistic': None, 'p_value': None}
        
        return {
            'statistiques': stats_desc,
            'theorique': {
                'probabilite': p,
                'longueur_moyenne_theorique': longueur_moyenne_theorique,
                'nombre_runs_theorique': nombre_runs_theorique
            },
            'tests': tests,
            'ecarts': {
                'ecart_longueur_moyenne': stats_desc['longueur_moyenne'] - longueur_moyenne_theorique,
                'ecart_nombre_runs': stats_desc['nombre_runs'] - nombre_runs_theorique
            }
        }
    
    def _analyser_runs_global(self, runs: list, sequence: list) -> dict:
        """Analyse globale des runs"""
        
        # Nombre total de runs
        nombre_total_runs = len(runs)
        
        # Longueurs de tous les runs
        toutes_longueurs = [length for _, length in runs]
        
        # Statistiques globales
        stats_globales = {
            'nombre_total_runs': nombre_total_runs,
            'longueur_moyenne_globale': np.mean(toutes_longueurs),
            'longueur_max_globale': max(toutes_longueurs),
            'distribution_globale': Counter(toutes_longueurs)
        }
        
        # Runs Test (test de randomness)
        # Implémentation simplifiée du runs test
        n = len(sequence)
        valeurs_uniques = list(set(sequence))
        
        if len(valeurs_uniques) == 2:
            # Runs test pour séquence binaire
            n1 = sequence.count(valeurs_uniques[0])
            n2 = sequence.count(valeurs_uniques[1])
            
            # Nombre de runs observé
            R = nombre_total_runs
            
            # Moyenne et variance théoriques
            mu_R = (2 * n1 * n2) / (n1 + n2) + 1
            sigma2_R = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2)**2 * (n1 + n2 - 1))
            sigma_R = np.sqrt(sigma2_R)
            
            # Z-score
            if sigma_R > 0:
                z_score = (R - mu_R) / sigma_R
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            else:
                z_score = 0
                p_value = 1
            
            stats_globales['runs_test'] = {
                'runs_observes': R,
                'runs_attendus': mu_R,
                'z_score': z_score,
                'p_value': p_value,
                'significatif': p_value < 0.05
            }
        
        return stats_globales
    
    def calculer_autocorrelation(self, sequence: list, max_lag: int = 20) -> dict:
        """
        Calcule l'autocorrélation de la séquence
        
        Args:
            sequence: Séquence à analyser
            max_lag: Nombre maximum de lags à calculer
            
        Returns:
            dict: Coefficients d'autocorrélation
        """
        # Convertir en valeurs numériques pour l'autocorrélation
        valeurs_uniques = list(set(sequence))
        mapping = {val: i for i, val in enumerate(valeurs_uniques)}
        sequence_num = [mapping[val] for val in sequence]
        
        autocorr = {}
        n = len(sequence_num)
        
        for lag in range(1, min(max_lag + 1, n // 4)):
            # Calcul de l'autocorrélation pour le lag donné
            x1 = sequence_num[:-lag]
            x2 = sequence_num[lag:]
            
            if len(x1) > 0 and len(x2) > 0:
                corr = np.corrcoef(x1, x2)[0, 1]
                autocorr[lag] = corr if not np.isnan(corr) else 0
        
        return autocorr
    
    def calculer_entropie_shannon(self, sequence: list) -> float:
        """
        Calcule l'entropie de Shannon de la séquence
        
        Args:
            sequence: Séquence à analyser
            
        Returns:
            float: Entropie de Shannon
        """
        # Compter les fréquences
        compteur = Counter(sequence)
        n = len(sequence)
        
        # Calculer l'entropie
        entropie = 0
        for count in compteur.values():
            p = count / n
            if p > 0:
                entropie -= p * np.log2(p)
        
        return entropie

    def analyser_toutes_sequences(self):
        """Lance l'analyse complète de toutes les séquences"""
        print("\n🚀 LANCEMENT DE L'ANALYSE COMPLÈTE")
        print("=" * 50)

        # Analyser chaque type de séquence
        for nom_index, sequence in self.sequences.items():
            print(f"\n📊 ANALYSE {nom_index}")
            print("-" * 30)

            # Analyse des runs
            resultats_runs = self.analyser_runs(sequence, nom_index)

            # Autocorrélation
            print(f"🔗 Calcul de l'autocorrélation...")
            autocorr = self.calculer_autocorrelation(sequence)

            # Entropie de Shannon
            print(f"🧮 Calcul de l'entropie de Shannon...")
            entropie = self.calculer_entropie_shannon(sequence)

            # Stocker les résultats
            self.resultats[nom_index] = {
                'runs': resultats_runs,
                'autocorrelation': autocorr,
                'entropie_shannon': entropie,
                'taille_sequence': len(sequence)
            }

            print(f"✅ Analyse {nom_index} terminée")

    def generer_rapport(self, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des analyses

        Args:
            fichier_sortie: Nom du fichier de rapport (optionnel)
        """
        if not fichier_sortie:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fichier_sortie = f"rapport_analyse_sequences_{timestamp}.txt"

        print(f"\n📝 Génération du rapport : {fichier_sortie}")

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("RAPPORT D'ANALYSE STATISTIQUE DES SÉQUENCES LUPASCO\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date d'analyse : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Fichier source : {self.fichier_json}\n")
            f.write(f"Nombre de parties : {len(self.donnees['parties']):,}\n\n")

            # Rapport pour chaque index
            for nom_index, resultats in self.resultats.items():
                f.write(f"\n{'='*20} ANALYSE {nom_index} {'='*20}\n\n")
                f.write(f"Taille de la séquence : {resultats['taille_sequence']:,} éléments\n")
                f.write(f"Entropie de Shannon : {resultats['entropie_shannon']:.4f} bits\n\n")

                # Analyse des runs par valeur
                f.write("ANALYSE DES RUNS PAR VALEUR :\n")
                f.write("-" * 40 + "\n")

                for valeur, stats in resultats['runs'].items():
                    if valeur == 'global':
                        continue

                    f.write(f"\n🎯 {valeur} :\n")

                    # Statistiques descriptives
                    stats_desc = stats['statistiques']
                    f.write(f"   Nombre de runs : {stats_desc['nombre_runs']:,}\n")
                    f.write(f"   Longueur moyenne : {stats_desc['longueur_moyenne']:.2f}\n")
                    f.write(f"   Longueur médiane : {stats_desc['longueur_mediane']:.2f}\n")
                    f.write(f"   Longueur max : {stats_desc['longueur_max']}\n")
                    f.write(f"   Écart-type : {stats_desc['ecart_type']:.2f}\n")

                    # Comparaison théorique
                    theorique = stats['theorique']
                    f.write(f"   Probabilité : {theorique['probabilite']:.4f}\n")
                    f.write(f"   Longueur moyenne théorique : {theorique['longueur_moyenne_theorique']:.2f}\n")

                    # Écarts
                    ecarts = stats['ecarts']
                    f.write(f"   Écart longueur moyenne : {ecarts['ecart_longueur_moyenne']:.2f}\n")

                    # Tests statistiques
                    if 'ks_test' in stats['tests'] and stats['tests']['ks_test']['p_value']:
                        ks = stats['tests']['ks_test']
                        f.write(f"   Test KS p-value : {ks['p_value']:.4f}\n")
                        f.write(f"   Test KS significatif : {'Oui' if ks['p_value'] < 0.05 else 'Non'}\n")

                    # Distribution des longueurs (top 10)
                    f.write("   Distribution des longueurs (top 10) :\n")
                    dist_sorted = sorted(stats_desc['distribution'].items(), key=lambda x: x[1], reverse=True)
                    for longueur, count in dist_sorted[:10]:
                        f.write(f"     Longueur {longueur} : {count:,} fois\n")

                # Analyse globale
                if 'global' in resultats['runs']:
                    global_stats = resultats['runs']['global']
                    f.write(f"\nANALYSE GLOBALE :\n")
                    f.write("-" * 20 + "\n")
                    f.write(f"Nombre total de runs : {global_stats['nombre_total_runs']:,}\n")
                    f.write(f"Longueur moyenne globale : {global_stats['longueur_moyenne_globale']:.2f}\n")
                    f.write(f"Longueur max globale : {global_stats['longueur_max_globale']}\n")

                    # Runs test
                    if 'runs_test' in global_stats:
                        rt = global_stats['runs_test']
                        f.write(f"\nRUNS TEST (Test de randomness) :\n")
                        f.write(f"   Runs observés : {rt['runs_observes']}\n")
                        f.write(f"   Runs attendus : {rt['runs_attendus']:.2f}\n")
                        f.write(f"   Z-score : {rt['z_score']:.4f}\n")
                        f.write(f"   P-value : {rt['p_value']:.4f}\n")
                        f.write(f"   Significatif : {'Oui' if rt['significatif'] else 'Non'}\n")

                # Autocorrélation
                f.write(f"\nAUTOCORRÉLATION :\n")
                f.write("-" * 15 + "\n")
                autocorr = resultats['autocorrelation']
                for lag in sorted(autocorr.keys())[:10]:  # Premiers 10 lags
                    f.write(f"   Lag {lag} : {autocorr[lag]:.4f}\n")

        print(f"✅ Rapport généré : {fichier_sortie}")
        return fichier_sortie

    def generer_graphiques(self, dossier_sortie: str = "graphiques_sequences"):
        """
        Génère des graphiques d'analyse

        Args:
            dossier_sortie: Dossier pour sauvegarder les graphiques
        """
        if not HAS_MATPLOTLIB:
            print("⚠️  Matplotlib non disponible, graphiques non générés")
            return

        import os

        if not os.path.exists(dossier_sortie):
            os.makedirs(dossier_sortie)

        print(f"\n📊 Génération des graphiques dans : {dossier_sortie}")

        for nom_index, resultats in self.resultats.items():
            # Graphique des distributions de longueurs de runs
            plt.figure(figsize=(12, 8))

            subplot_idx = 1
            valeurs = [v for v in resultats['runs'].keys() if v != 'global']
            n_valeurs = len(valeurs)

            for valeur in valeurs:
                plt.subplot(2, (n_valeurs + 1) // 2, subplot_idx)

                stats_desc = resultats['runs'][valeur]['statistiques']
                distribution = stats_desc['distribution']

                longueurs = list(distribution.keys())
                frequences = list(distribution.values())

                plt.bar(longueurs, frequences, alpha=0.7)
                plt.title(f'Distribution runs {valeur}')
                plt.xlabel('Longueur du run')
                plt.ylabel('Fréquence')
                plt.grid(True, alpha=0.3)

                subplot_idx += 1

            plt.tight_layout()
            plt.savefig(f"{dossier_sortie}/distribution_runs_{nom_index}.png", dpi=300, bbox_inches='tight')
            plt.close()

            # Graphique d'autocorrélation
            plt.figure(figsize=(10, 6))
            autocorr = resultats['autocorrelation']
            lags = list(autocorr.keys())
            correlations = list(autocorr.values())

            plt.plot(lags, correlations, 'bo-', markersize=4)
            plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
            plt.title(f'Autocorrélation - {nom_index}')
            plt.xlabel('Lag')
            plt.ylabel('Coefficient d\'autocorrélation')
            plt.grid(True, alpha=0.3)

            plt.savefig(f"{dossier_sortie}/autocorrelation_{nom_index}.png", dpi=300, bbox_inches='tight')
            plt.close()

        print(f"✅ Graphiques générés dans {dossier_sortie}/")


def main():
    """Fonction principale du programme"""
    import sys

    print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
    print("=" * 60)
    print("Basé sur : recherche_analyse_statistique_sequences.txt")
    print()

    # Vérifier les arguments
    if len(sys.argv) < 2:
        print("❌ Erreur : Fichier JSON requis")
        print("\nUsage:")
        print("  python analyseur_sequences_lupasco.py <fichier_json>")
        print("\nExemple:")
        print("  python analyseur_sequences_lupasco.py dataset_baccarat_lupasco_20250617_232800.json")
        sys.exit(1)

    fichier_json = sys.argv[1]

    try:
        # Créer l'analyseur
        analyseur = AnalyseurSequencesLupasco(fichier_json)

        # Charger les données
        analyseur.charger_donnees()

        # Lancer l'analyse complète
        analyseur.analyser_toutes_sequences()

        # Générer le rapport
        fichier_rapport = analyseur.generer_rapport()

        # Générer les graphiques
        try:
            analyseur.generer_graphiques()
        except ImportError:
            print("⚠️  Matplotlib non disponible, graphiques non générés")
        except Exception as e:
            print(f"⚠️  Erreur lors de la génération des graphiques : {e}")

        print(f"\n🎉 ANALYSE TERMINÉE AVEC SUCCÈS !")
        print(f"📝 Rapport détaillé : {fichier_rapport}")
        print(f"📊 Graphiques : dossier graphiques_sequences/")

    except Exception as e:
        print(f"\n❌ ERREUR : {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
