#!/usr/bin/env python3
"""
EXTRACTEUR DE MÉTHODES ESSENTIELLES
===================================

Programme pour extraire les méthodes spécifiques à conserver
et les copier dans un fichier texte.

Date: 2025-06-18
"""

import re
import os

def extraire_methode_complete(fichier_source, nom_methode, ligne_debut_approx):
    """
    Extrait une méthode complète depuis le fichier source
    """
    with open(fichier_source, 'r', encoding='utf-8') as f:
        lignes = f.readlines()
    
    # Chercher la méthode par nom (plus fiable que par ligne)
    debut_trouve = False
    indentation_methode = None
    methode_lignes = []
    ligne_debut_reelle = None
    
    for i, ligne in enumerate(lignes):
        # Chercher la définition de la méthode
        if f"def {nom_methode}(" in ligne or f"def {nom_methode} (" in ligne:
            debut_trouve = True
            ligne_debut_reelle = i + 1
            indentation_methode = len(ligne) - len(ligne.lstrip())
            methode_lignes.append(ligne)
        elif debut_trouve:
            # Vérifier si on est encore dans la méthode
            if ligne.strip() == "":
                # Ligne vide, continuer
                methode_lignes.append(ligne)
            elif len(ligne) - len(ligne.lstrip()) > indentation_methode:
                # Ligne indentée, fait partie de la méthode
                methode_lignes.append(ligne)
            elif ligne.strip().startswith('#') and len(ligne) - len(ligne.lstrip()) >= indentation_methode:
                # Commentaire au même niveau ou indenté, peut faire partie de la méthode
                methode_lignes.append(ligne)
            else:
                # Nouvelle méthode ou fin de classe, arrêter
                break
    
    if not debut_trouve:
        return None, None
    
    return ''.join(methode_lignes), ligne_debut_reelle

def extraire_methodes_essentielles():
    """
    Extrait toutes les méthodes essentielles listées
    """
    fichier_source = '../analyseur_sequences_lupasco.py'
    
    if not os.path.exists(fichier_source):
        print(f"❌ Fichier source non trouvé : {fichier_source}")
        return
    
    # Liste des méthodes à extraire (noms exacts du fichier source)
    methodes_a_extraire = [
        'generer_rapport',
        'analyser_runs',
        'calculer_entropie_shannon',
        '_analyser_runs_global',  # Contient le runs test
        'calculer_autocorrelation',
        '_extraire_sequences',
        '_analyser_runs_valeur',  # Nom correct
        '__init__',               # Constructeur nécessaire
        'charger_donnees',        # Chargement nécessaire
        '_charger_avec_streaming', # Support chargement
        '_charger_standard',      # Support chargement
        'analyser_toutes_sequences' # Méthode principale d'analyse
    ]
    
    print(f"🔍 Extraction de {len(methodes_a_extraire)} méthodes essentielles...")
    
    # Créer le fichier d'extraction
    with open('methodes_essentielles_extraites.txt', 'w', encoding='utf-8') as f:
        f.write("MÉTHODES ESSENTIELLES EXTRAITES - ANALYSEUR LUPASCO\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Date d'extraction : 2025-06-18\n")
        f.write(f"Source : {fichier_source}\n")
        f.write(f"Nombre de méthodes : {len(methodes_a_extraire)}\n\n")
        
        methodes_trouvees = 0
        
        for i, nom_methode in enumerate(methodes_a_extraire, 1):
            print(f"   Extraction {i}/{len(methodes_a_extraire)} : {nom_methode}")
            
            # Extraire la méthode
            code_complet, ligne_debut = extraire_methode_complete(fichier_source, nom_methode, 0)
            
            if code_complet:
                f.write(f"\n{'='*80}\n")
                f.write(f"MÉTHODE {i}/{len(methodes_a_extraire)} : {nom_methode}\n")
                f.write(f"{'='*80}\n")
                f.write(f"Ligne de début : {ligne_debut}\n")
                f.write(f"Taille : {len(code_complet.splitlines())} lignes\n")
                f.write(f"\n{'-'*60}\n")
                f.write("CODE COMPLET :\n")
                f.write(f"{'-'*60}\n\n")
                
                f.write(code_complet)
                
                f.write(f"\n{'-'*60}\n")
                f.write("FIN DE MÉTHODE\n")
                f.write(f"{'-'*60}\n\n")
                
                methodes_trouvees += 1
            else:
                print(f"      ❌ Méthode {nom_methode} non trouvée")
                f.write(f"\n❌ MÉTHODE NON TROUVÉE : {nom_methode}\n\n")
    
    print(f"✅ Extraction terminée : {methodes_trouvees}/{len(methodes_a_extraire)} méthodes trouvées")
    print(f"📄 Fichier généré : methodes_essentielles_extraites.txt")
    
    # Résumé
    print(f"\n📋 RÉSUMÉ DES MÉTHODES EXTRAITES :")
    for i, nom_methode in enumerate(methodes_a_extraire, 1):
        print(f"   {i}. {nom_methode}")

if __name__ == "__main__":
    extraire_methodes_essentielles()
