#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CONVERTISSEUR JSON VERS TXT - SYSTÈME LUPASCO
==============================================

Ce programme convertit un fichier JSON généré par le générateur de parties Baccarat Lupasco
en fichier TXT au format identique à celui produit par la méthode exporter_txt().

Usage:
    python convertisseur_json_vers_txt.py <fichier_json> [fichier_txt_sortie]

Exemples:
    python convertisseur_json_vers_txt.py dataset_baccarat_lupasco_20250617_230713.json
    python convertisseur_json_vers_txt.py dataset.json dataset_converti.txt

Auteur: Générateur Lupasco
Date: 2025-06-17
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any


def convertir_json_vers_txt(fichier_json: str, fichier_txt: str = None) -> str:
    """
    Convertit un fichier JSON Lupasco en fichier TXT au format standard
    
    Args:
        fichier_json: Chemin vers le fichier JSON source
        fichier_txt: Chemin vers le fichier TXT de sortie (optionnel)
    
    Returns:
        str: Chemin du fichier TXT généré
    """
    
    # Vérifier que le fichier JSON existe
    if not os.path.exists(fichier_json):
        raise FileNotFoundError(f"Le fichier JSON '{fichier_json}' n'existe pas.")
    
    # Générer le nom du fichier TXT si non fourni
    if fichier_txt is None:
        base_name = os.path.splitext(fichier_json)[0]
        fichier_txt = f"{base_name}_converti.txt"
    
    print(f"🔄 Conversion JSON vers TXT en cours...")
    print(f"📂 Fichier source : {fichier_json}")
    print(f"📝 Fichier destination : {fichier_txt}")
    
    # Charger le fichier JSON
    try:
        with open(fichier_json, 'r', encoding='utf-8') as jsonfile:
            data = json.load(jsonfile)
    except json.JSONDecodeError as e:
        raise ValueError(f"Erreur lors de la lecture du fichier JSON : {e}")
    except Exception as e:
        raise Exception(f"Erreur lors de l'ouverture du fichier JSON : {e}")
    
    # Vérifier la structure du JSON
    if "metadata" not in data or "parties" not in data:
        raise ValueError("Structure JSON invalide : 'metadata' et 'parties' requis.")
    
    metadata = data["metadata"]
    parties = data["parties"]
    
    print(f"📊 Nombre de parties à convertir : {len(parties)}")
    
    # Générer le fichier TXT
    with open(fichier_txt, 'w', encoding='utf-8') as txtfile:
        # En-tête du fichier (format identique à exporter_txt)
        txtfile.write("GÉNÉRATEUR PARTIES BACCARAT LUPASCO\n")
        txtfile.write("=" * 50 + "\n")
        
        # Utiliser la date de génération du JSON ou la date actuelle
        if "date_generation" in metadata:
            try:
                # Convertir ISO format vers format lisible
                date_iso = metadata["date_generation"]
                date_obj = datetime.fromisoformat(date_iso.replace('Z', '+00:00'))
                date_str = date_obj.strftime('%Y-%m-%d %H:%M:%S')
            except:
                date_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            date_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        txtfile.write(f"Généré le : {date_str}\n")
        txtfile.write(f"Nombre de parties : {len(parties)}\n")
        txtfile.write("Hasard cryptographiquement sécurisé\n")
        txtfile.write("=" * 50 + "\n\n")
        
        # Traiter chaque partie
        for partie_data in parties:
            partie_number = partie_data["partie_number"]
            burn_info = partie_data["burn_info"]
            mains = partie_data["mains"]
            
            # Informations de brûlage en en-tête de chaque partie
            burn_cards_count = burn_info["burn_cards_count"]
            initial_sync_state = burn_info["initial_sync_state"]
            
            # Première carte brûlée
            premiere_carte_data = burn_info.get("premiere_carte_brulee")
            if premiere_carte_data:
                carte_str = f"{premiere_carte_data['rang']}{premiere_carte_data['couleur']}"
            else:
                carte_str = "N/A"
            
            # En-tête de la partie (format identique à exporter_txt)
            txtfile.write(f"PARTIE {partie_number}\n")
            txtfile.write(f"BURN: {burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {initial_sync_state}\n")
            txtfile.write("-" * 70 + "\n")
            txtfile.write("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5\n")
            txtfile.write("-" * 55 + "\n")
            
            # Traiter chaque main
            for main_data in mains:
                main_number = main_data["main_number"]
                manche_pb_number = main_data["manche_pb_number"]
                index1_sync_state = main_data["index1_sync_state"]
                index2_cards_category = main_data["index2_cards_category"]
                index3_result = main_data["index3_result"]
                index5_combined = main_data["index5_combined"]
                
                # Formater le numéro de manche (format identique à exporter_txt)
                manche_str = str(manche_pb_number) if manche_pb_number is not None else "-"
                
                # Ligne de données (format identique à exporter_txt)
                ligne = f"{main_number:4d} | {manche_str:6s} | {index1_sync_state:6s} | {index2_cards_category:8s} | {index3_result:6s} | {index5_combined}"
                txtfile.write(ligne + "\n")
            
            txtfile.write("\n")  # Ligne vide entre parties
    
    print(f"✅ Conversion terminée avec succès !")
    print(f"📝 Fichier TXT généré : {fichier_txt}")
    
    return fichier_txt


def mode_interactif():
    """Mode interactif avec détection automatique des fichiers JSON"""
    import glob

    print("🔄 CONVERSION RAPIDE JSON → TXT")
    print("=" * 40)

    # Chercher les fichiers JSON dans le répertoire courant
    fichiers_json = glob.glob("*.json")

    if not fichiers_json:
        print("❌ Aucun fichier JSON trouvé dans le répertoire courant.")
        return

    print(f"📂 {len(fichiers_json)} fichier(s) JSON trouvé(s) :")
    for i, fichier in enumerate(fichiers_json, 1):
        taille = os.path.getsize(fichier)
        taille_mb = taille / (1024 * 1024)
        print(f"  {i}. {fichier} ({taille_mb:.1f} MB)")

    # Demander à l'utilisateur de choisir
    if len(fichiers_json) == 1:
        choix = 1
        print(f"\n🎯 Conversion automatique du seul fichier : {fichiers_json[0]}")
    else:
        try:
            choix = int(input(f"\n🎯 Choisissez un fichier (1-{len(fichiers_json)}) : "))
            if choix < 1 or choix > len(fichiers_json):
                print("❌ Choix invalide.")
                return
        except ValueError:
            print("❌ Veuillez entrer un nombre valide.")
            return

    fichier_choisi = fichiers_json[choix - 1]

    try:
        print(f"\n🔄 Conversion en cours...")
        fichier_genere = convertir_json_vers_txt(fichier_choisi)

        # Afficher les informations sur les fichiers
        taille_json = os.path.getsize(fichier_choisi) / (1024 * 1024)
        taille_txt = os.path.getsize(fichier_genere) / (1024 * 1024)

        print(f"\n✅ CONVERSION RÉUSSIE !")
        print(f"📂 JSON source : {fichier_choisi} ({taille_json:.1f} MB)")
        print(f"📝 TXT généré : {fichier_genere} ({taille_txt:.1f} MB)")
        print(f"📊 Ratio de compression : {taille_txt/taille_json:.1f}x")

    except Exception as e:
        print(f"\n❌ ERREUR : {e}")


def mode_ligne_commande():
    """Mode ligne de commande avec arguments"""
    print("🔄 CONVERTISSEUR JSON VERS TXT - SYSTÈME LUPASCO")
    print("=" * 60)

    fichier_json = sys.argv[1]
    fichier_txt = sys.argv[2] if len(sys.argv) > 2 else None

    try:
        fichier_genere = convertir_json_vers_txt(fichier_json, fichier_txt)
        print(f"\n🎉 CONVERSION RÉUSSIE !")
        print(f"📂 Fichier source : {fichier_json}")
        print(f"📝 Fichier généré : {fichier_genere}")

    except Exception as e:
        print(f"\n❌ ERREUR : {e}")
        sys.exit(1)


def afficher_aide():
    """Affiche l'aide du programme"""
    print("🔄 CONVERTISSEUR JSON VERS TXT - SYSTÈME LUPASCO")
    print("=" * 60)
    print()
    print("MODES D'UTILISATION :")
    print()
    print("1️⃣  MODE INTERACTIF (recommandé) :")
    print("   python convertisseur_json_vers_txt.py")
    print("   → Détecte automatiquement les fichiers JSON et propose une interface")
    print()
    print("2️⃣  MODE LIGNE DE COMMANDE :")
    print("   python convertisseur_json_vers_txt.py <fichier_json> [fichier_txt_sortie]")
    print()
    print("EXEMPLES :")
    print("   python convertisseur_json_vers_txt.py")
    print("   python convertisseur_json_vers_txt.py dataset.json")
    print("   python convertisseur_json_vers_txt.py dataset.json mon_fichier.txt")
    print()
    print("OPTIONS :")
    print("   -h, --help    Affiche cette aide")
    print()
    print("DESCRIPTION :")
    print("   Convertit un fichier JSON généré par le générateur Lupasco")
    print("   en fichier TXT au format identique à la méthode exporter_txt().")


def main():
    """Fonction principale du programme fusionné"""

    # Vérifier les options d'aide
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        afficher_aide()
        return

    # Mode interactif si aucun argument
    if len(sys.argv) == 1:
        mode_interactif()

    # Mode ligne de commande si arguments fournis
    elif len(sys.argv) >= 2:
        mode_ligne_commande()

    else:
        afficher_aide()


if __name__ == "__main__":
    main()
