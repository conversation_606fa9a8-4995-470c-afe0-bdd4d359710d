# HARVARD UNIVERSITY - MUTUAL INFORMATION ANALYSIS

**Source :** https://journals.plos.org/plosone/article?id=10.1371/journal.pone.0284904

## INFORMATION MUTUELLE (MI)

### DÉFINITION THÉORIQUE
- **Mesure de dépendance non-linéaire** : capture toutes formes de relations
- **Formule** : I(X;Y) = Σ P(x,y) × log[P(x,y)/(P(x)×P(y))]
- **Unité** : bits ou nats
- **Propriétés** : I(X;Y) ≥ 0, I(X;Y) = 0 ⟺ indépendance

### AVANTAGES SUR CORRÉLATION
- **Détecte relations non-linéaires** : pas seulement linéaires
- **Robuste aux transformations** : invariant monotone
- **Mesure universelle** : applicable à tous types de variables
- **Pas d'hypothèse de distribution** : non-paramétrique

### CALCUL PRATIQUE
```
1. Estimer distributions jointes P(x,y)
2. Estimer distributions marginales P(x), P(y)
3. Calculer MI = Σ P(x,y) × log[P(x,y)/(P(x)×P(y))]
4. Normaliser si nécessaire
```

### INTERPRÉTATION
- **MI = 0** : indépendance statistique parfaite
- **MI > 0** : dépendance (plus élevé = plus dépendant)
- **MI normalisée** : entre 0 et 1 pour comparaisons

## APPLICATION LUPASCO

### POUR RELATIONS INDEX1-INDEX2-INDEX3
- **Détection de dépendances cachées** : au-delà de Chi²
- **Mesure précise** : quantification de l'information partagée
- **Comparaison objective** : entre différentes paires d'INDEX

### VALIDATION SYSTÈME LUPASCO
- **MI(INDEX2(t), INDEX1(t+1))** : validation règle causale
- **MI(INDEX1, INDEX2)** : dépendance synchrone
- **MI(INDEX2, INDEX3)** : influence sur prédiction finale

### AVANTAGES SPÉCIFIQUES
- Capture relations complexes non détectées par Chi²
- Mesure robuste pour variables catégorielles
- Validation indépendante des tests classiques
