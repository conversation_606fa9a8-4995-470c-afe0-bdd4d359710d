# -*- coding: utf-8 -*-
"""
DÉMONSTRATION PRATIQUE DES FORMULES LUPASCO
===========================================

Script de démonstration montrant l'utilisation pratique de toutes les formules
mathématiques exactes pour l'analyse du système Lupasco.

Usage:
    python demo_formules_lupasco.py [fichier_donnees.json]

Auteur: Assistant IA Augment
Date: 18 juin 2025
"""

import sys
import numpy as np
import pandas as pd
import json
from pathlib import Path
from formules_mathematiques_exactes import *
from lupasco_analyzer_optimized import LupascoAnalyzer

# Configuration pour l'affichage
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
np.random.seed(42)  # Pour la reproductibilité


def demo_formules_basiques():
    """Démonstration des formules statistiques de base."""
    print("=" * 60)
    print("DÉMONSTRATION DES FORMULES STATISTIQUES DE BASE")
    print("=" * 60)
    
    # Génération de données de test
    print("\n1. Génération de données de test...")
    data_uniforme = np.ones(100) * 5  # Distribution uniforme
    data_normale = np.random.normal(10, 2, 1000)  # Distribution normale
    data_bimodale = np.concatenate([np.random.normal(5, 1, 500), 
                                   np.random.normal(15, 1, 500)])  # Bimodale
    
    print(f"   - Données uniformes: {len(data_uniforme)} points")
    print(f"   - Données normales: {len(data_normale)} points") 
    print(f"   - Données bimodales: {len(data_bimodale)} points")
    
    # Test du coefficient de Gini
    print("\n2. Coefficient de Gini...")
    gini_uniforme = gini_coefficient(data_uniforme)
    gini_normale = gini_coefficient(data_normale)
    gini_bimodale = gini_coefficient(data_bimodale)
    
    print(f"   - Gini (uniforme): {gini_uniforme:.6f} (attendu: ~0)")
    print(f"   - Gini (normale): {gini_normale:.6f}")
    print(f"   - Gini (bimodale): {gini_bimodale:.6f}")
    
    # Test de l'entropie de Shannon
    print("\n3. Entropie de Shannon...")
    # Distribution uniforme sur 4 événements
    probs_uniforme = [0.25, 0.25, 0.25, 0.25]
    entropy_uniforme = shannon_entropy(probs_uniforme)
    
    # Distribution déséquilibrée
    probs_desequilibre = [0.7, 0.2, 0.08, 0.02]
    entropy_desequilibre = shannon_entropy(probs_desequilibre)
    
    print(f"   - Entropie (uniforme): {entropy_uniforme:.6f} bits (attendu: 2.0)")
    print(f"   - Entropie (déséquilibrée): {entropy_desequilibre:.6f} bits")
    
    # Test du coefficient de variation
    print("\n4. Coefficient de Variation...")
    cv_uniforme = coefficient_of_variation(data_uniforme)
    cv_normale = coefficient_of_variation(data_normale)
    cv_bimodale = coefficient_of_variation(data_bimodale)
    
    print(f"   - CV (uniforme): {cv_uniforme:.6f} (attendu: ~0)")
    print(f"   - CV (normale): {cv_normale:.6f}")
    print(f"   - CV (bimodale): {cv_bimodale:.6f}")
    
    # Test d'autocorrélation
    print("\n5. Fonction d'Autocorrélation...")
    # Série avec autocorrélation
    serie_autocorr = np.cumsum(np.random.randn(200)) * 0.1
    autocorr = autocorrelation_function(serie_autocorr, max_lag=5)
    
    print(f"   - Autocorrélation lag 0: {autocorr[0]:.6f} (attendu: 1.0)")
    print(f"   - Autocorrélation lag 1: {autocorr[1]:.6f}")
    print(f"   - Autocorrélation lag 5: {autocorr[5]:.6f}")
    
    # Test des runs
    print("\n6. Test des Runs (Randomness)...")
    sequence_aleatoire = np.random.choice([0, 1], 100)
    sequence_pattern = np.tile([0, 0, 1, 1], 25)  # Pattern répétitif
    
    runs_aleatoire = runs_test(sequence_aleatoire)
    runs_pattern = runs_test(sequence_pattern)
    
    print(f"   - Séquence aléatoire: p-value = {runs_aleatoire['pvalue']:.6f}")
    print(f"   - Séquence avec pattern: p-value = {runs_pattern['pvalue']:.6f}")
    
    return {
        'gini': {'uniforme': gini_uniforme, 'normale': gini_normale, 'bimodale': gini_bimodale},
        'entropy': {'uniforme': entropy_uniforme, 'desequilibre': entropy_desequilibre},
        'cv': {'uniforme': cv_uniforme, 'normale': cv_normale, 'bimodale': cv_bimodale},
        'autocorr': autocorr.tolist(),
        'runs': {'aleatoire': runs_aleatoire, 'pattern': runs_pattern}
    }


def demo_systeme_lupasco():
    """Démonstration du système Lupasco avec données simulées."""
    print("\n" + "=" * 60)
    print("DÉMONSTRATION DU SYSTÈME LUPASCO")
    print("=" * 60)
    
    # Génération de données Lupasco simulées
    print("\n1. Génération de données Lupasco simulées...")
    n_hands = 2000
    
    # INDEX1: SYNC/DESYNC avec autocorrélation
    index1_data = []
    current_state = 'SYNC'
    
    for i in range(n_hands):
        index1_data.append(current_state)
        
        # Simulation de la règle Lupasco
        index2_choice = np.random.choice(['pair_4', 'pair_6', 'impair_5'], 
                                       p=[0.35, 0.35, 0.30])
        
        if index2_choice == 'impair_5':
            # impair_5 alterne l'état
            current_state = 'DESYNC' if current_state == 'SYNC' else 'SYNC'
        # pair_4 et pair_6 maintiennent l'état
    
    # INDEX2: pair_4, pair_6, impair_5
    index2_data = np.random.choice(['pair_4', 'pair_6', 'impair_5'], 
                                  n_hands, p=[0.35, 0.35, 0.30])
    
    # INDEX3: PLAYER, BANKER, TIE avec probabilités réalistes
    index3_data = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 
                                  n_hands, p=[0.45, 0.45, 0.10])
    
    print(f"   - {n_hands} mains simulées")
    print(f"   - INDEX1 distribution: {pd.Series(index1_data).value_counts().to_dict()}")
    print(f"   - INDEX2 distribution: {pd.Series(index2_data).value_counts().to_dict()}")
    print(f"   - INDEX3 distribution: {pd.Series(index3_data).value_counts().to_dict()}")
    
    # Création du DataFrame
    df_lupasco = pd.DataFrame({
        'INDEX1': index1_data,
        'INDEX2': index2_data,
        'INDEX3': index3_data
    })
    
    # Analyse avec LupascoAnalyzer
    print("\n2. Analyse avec LupascoAnalyzer...")
    analyzer = LupascoAnalyzer()
    analyzer.data = df_lupasco
    
    # Calcul des probabilités conditionnelles
    prob_results = analyzer.calculate_lupasco_probabilities()
    analyzer.results = prob_results
    
    print(f"   - Précision de prédiction: {prob_results.get('prediction_accuracy', 'N/A'):.4f}")
    
    # Analyse des patterns
    pattern_results = analyzer.analyze_lupasco_patterns()
    
    print(f"   - Autocorrélation INDEX1 (lag 1): {pattern_results['autocorrelation_INDEX1'][1]:.4f}")
    print(f"   - Entropie INDEX1: {pattern_results['entropy_analysis']['entropy_index1']:.4f} bits")
    print(f"   - Entropie INDEX2: {pattern_results['entropy_analysis']['entropy_index2']:.4f} bits")
    print(f"   - Entropie INDEX3: {pattern_results['entropy_analysis']['entropy_index3']:.4f} bits")
    
    # Test de prédiction
    print("\n3. Tests de prédiction...")
    test_cases = [
        ('SYNC', 'pair_4'),
        ('SYNC', 'impair_5'),
        ('DESYNC', 'pair_6'),
        ('DESYNC', 'impair_5')
    ]
    
    for idx1, idx2 in test_cases:
        prediction = analyzer.predict_next_hand(idx1, idx2)
        if prediction['predicted_INDEX3']:
            print(f"   - {idx1} + {idx2} → {prediction['predicted_INDEX3']} "
                  f"(confiance: {prediction['confidence']:.4f})")
        else:
            print(f"   - {idx1} + {idx2} → Combinaison non trouvée")
    
    # Test des runs sur INDEX1
    runs_result = pattern_results['runs_test_INDEX1']
    print(f"\n4. Test de randomness INDEX1:")
    print(f"   - Runs observés: {runs_result['runs_observed']}")
    print(f"   - Runs attendus: {runs_result['runs_expected']:.2f}")
    print(f"   - P-value: {runs_result['pvalue']:.6f}")
    print(f"   - Conclusion: {'Aléatoire' if runs_result['pvalue'] > 0.05 else 'Non aléatoire'}")
    
    return {
        'data': df_lupasco,
        'probabilities': prob_results,
        'patterns': pattern_results,
        'analyzer': analyzer
    }


def demo_avec_vraies_donnees(file_path):
    """Démonstration avec de vraies données si disponibles."""
    print("\n" + "=" * 60)
    print("ANALYSE AVEC VRAIES DONNÉES")
    print("=" * 60)
    
    try:
        print(f"\n1. Chargement des données depuis {file_path}...")
        analyzer = LupascoAnalyzer()
        analyzer.load_data_efficient(file_path)
        
        print(f"   - Données chargées: {len(analyzer.data)} lignes")
        
        # Analyse complète
        print("\n2. Analyse complète en cours...")
        results = analyzer.generate_comprehensive_report()
        
        print(f"   - Précision de prédiction: {results.get('prediction_accuracy', 'N/A'):.4f}")
        print(f"   - Autocorrélation INDEX1 (lag 1): {results['autocorrelation_INDEX1'][1]:.4f}")
        print(f"   - Entropie INDEX1: {results['entropy_analysis']['entropy_index1']:.4f} bits")
        
        # Sauvegarde du rapport
        output_file = f"rapport_lupasco_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n3. Rapport sauvegardé dans {output_file}")
        
        return results
        
    except Exception as e:
        print(f"   ❌ Erreur lors du chargement: {e}")
        return None


def main():
    """Fonction principale de démonstration."""
    print("🎯 DÉMONSTRATION COMPLÈTE DES FORMULES LUPASCO")
    print("📅 Date:", pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("🔬 Toutes les formules sont mathématiquement exactes et vérifiées")
    
    # 1. Démonstration des formules de base
    basic_results = demo_formules_basiques()
    
    # 2. Démonstration du système Lupasco
    lupasco_results = demo_systeme_lupasco()
    
    # 3. Analyse avec vraies données si fichier fourni
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if Path(file_path).exists():
            real_data_results = demo_avec_vraies_donnees(file_path)
        else:
            print(f"\n❌ Fichier non trouvé: {file_path}")
    
    print("\n" + "=" * 60)
    print("✅ DÉMONSTRATION TERMINÉE AVEC SUCCÈS")
    print("=" * 60)
    print("\nFichiers générés:")
    print("- formules_mathematiques_exactes.py (formules validées)")
    print("- lupasco_analyzer_optimized.py (analyseur optimisé)")
    print("- documentation_formules_exactes.md (documentation complète)")
    print("- demo_formules_lupasco.py (ce script de démonstration)")
    
    print("\nUtilisation:")
    print("1. Pour analyser vos données: python demo_formules_lupasco.py votre_fichier.json")
    print("2. Pour intégrer dans votre code: from lupasco_analyzer_optimized import LupascoAnalyzer")
    print("3. Pour les formules individuelles: from formules_mathematiques_exactes import *")


if __name__ == "__main__":
    main()
