# REAL STATISTICS - EFFECT SIZE CHI-SQUARE

**Source :** https://real-statistics.com/chi-square-and-f-distributions/effect-size-chi-square/

## MESURES D'EFFET POUR CHI²

### 1. COEFFICIENT PHI (φ)
- **Usage** : Tableaux 2×2 uniquement
- **Formule** : φ = √(χ²/n)
- **Range** : -1 à +1
- **Interprétation** : comme corrélation de <PERSON>

### 2. CRAMÉR'S V
- **Usage** : Tous tableaux de contingence
- **Formule** : V = √(χ²/(n × min(r-1, c-1)))
- **Range** : 0 à 1
- **Interprétation** :
  - 0.1 = petit effet
  - 0.3 = effet moyen  
  - 0.5 = grand effet

### 3. COEFFICIENT DE CONTINGENCE
- **Formule** : C = √(χ²/(χ² + n))
- **Range** : 0 à √((k-1)/k)
- **Limitation** : maximum dépend de la taille du tableau

### CALCUL DANS EXCEL
```
=SQRT(CHISQ.TEST(observed_range, expected_range)/(n*MIN(rows-1, cols-1)))
```

## RECOMMANDATIONS PRATIQUES

### CHOIX DE LA MESURE
- **Tableaux 2×2** : Phi ou Cramér's V
- **Tableaux plus grands** : Cramér's V recommandé
- **Comparaisons** : Cramér's V pour standardisation

### SEUILS D'INTERPRÉTATION
- **V < 0.1** : association négligeable
- **0.1 ≤ V < 0.3** : association faible
- **0.3 ≤ V < 0.5** : association modérée
- **V ≥ 0.5** : association forte

## APPLICATION LUPASCO

### POUR INDEX1-INDEX2 (2×3)
- Cramér's V optimal pour ce format
- Quantification précise de l'indépendance observée
- Comparaison avec seuils académiques

### POUR INDEX3 PRÉDICTIONS (2×2)
- Phi ou Cramér's V pour PLAYER/BANKER
- Mesure de l'effet prédictif
- Validation de la performance du système
