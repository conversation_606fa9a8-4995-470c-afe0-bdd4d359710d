#!/usr/bin/env python3
"""
ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO - VERSION SIMPLIFIÉE
================================================================

Version simplifiée gardant uniquement les méthodes d'analyse des runs
et de génération du rapport de base.

Source : ../analyseur_sequences_lupasco.py
Nombre de méthodes : 12

Date: 2025-06-18
"""

import json
import ijson
import numpy as np
import os
import gc
from collections import Counter, defaultdict
from datetime import datetime
from scipy import stats
from scipy.stats import geom, kstest, chi2_contingency
import math
import sys
import pandas as pd

# Importer les formules mathématiques exactes
sys.path.append('..')
from formules_mathematiques_exactes import (
    gini_coefficient, shannon_entropy_from_data,
    autocorrelation_function, coefficient_of_variation,
    runs_test, lupasco_entropy_analysis
)

# Vérification ijson (requis pour gros fichiers)
try:
    import ijson
    HAS_IJSON = True
except ImportError:
    HAS_IJSON = False

# Vérification matplotlib (optionnel)
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences Lupasco - Version simplifiée
    """

    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec le fichier JSON

        Args:
            fichier_json: Chemin vers le fichier JSON du dataset Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        self.nb_parties_total = 0

        print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
        print("=" * 60)
        print(f"📂 Fichier à analyser : {fichier_json}")

    def charger_donnees(self):
        """Charge et extrait les données du fichier JSON volumineux (7GB) avec streaming"""
        print("\n📊 Chargement des données...")

        try:
            # Obtenir la taille du fichier
            taille_fichier = os.path.getsize(self.fichier_json)
            taille_gb = taille_fichier / (1024 * 1024 * 1024)
            taille_mb = taille_fichier / (1024 * 1024)

            if taille_gb >= 1:
                print(f"📁 Taille du fichier : {taille_gb:.1f} GB")
                print("🚨 FICHIER TRÈS VOLUMINEUX DÉTECTÉ !")

                if HAS_IJSON:
                    print("⚡ Utilisation du mode streaming ijson pour éviter les problèmes de mémoire...")
                    self._charger_avec_streaming()
                else:
                    print("❌ Module ijson requis pour les fichiers > 1 GB")
                    print("💡 Installation : pip install ijson")
                    raise ImportError("ijson requis pour les gros fichiers")
            else:
                print(f"📁 Taille du fichier : {taille_mb:.1f} MB")
                print("🔄 Chargement standard...")
                self._charger_standard()

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise

    def _charger_avec_streaming(self):
        """Charge le fichier JSON avec streaming ijson (pour fichiers 7GB)"""
        print("🔄 Lecture streaming du fichier JSON...")

        # Initialiser les listes de séquences directement
        index1_seq = []
        index2_seq = []
        index3_seq = []
        index5_seq = []  # INDEX5 : Combinaisons complètes
        index1_index2_seq = []
        index1_index3_seq = []
        index2_index3_seq = []
        index1_index2_index3_seq = []

        total_parties = 0
        total_mains = 0

        # Nettoyage mémoire préalable
        gc.collect()

        try:
            with open(self.fichier_json, 'rb') as f:
                print("   📖 Parsing streaming des parties...")

                # Parser streaming des parties
                parties = ijson.items(f, 'parties.item')

                for partie in parties:
                    total_parties += 1

                    # Traiter chaque main de la partie
                    for main in partie['mains']:
                        idx1 = main['index1_sync_state']
                        idx2 = main['index2_cards_category']
                        idx3 = main['index3_result']

                        # INDEX individuels
                        index1_seq.append(idx1)
                        index2_seq.append(idx2)
                        index3_seq.append(idx3)  # Garder tous les résultats y compris TIE

                        # INDEX5 : Combinaison complète
                        idx5 = main['index5_combined']
                        index5_seq.append(idx5)

                        # Combinaisons pour compatibilité
                        if idx3 in ['PLAYER', 'BANKER']:
                            # Combinaisons avec INDEX3
                            index1_index3_seq.append(f"{idx1}_{idx3}")
                            index2_index3_seq.append(f"{idx2}_{idx3}")
                            index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                        # Combinaisons sans INDEX3
                        index1_index2_seq.append(f"{idx1}_{idx2}")

                        total_mains += 1

                    # Affichage du progrès tous les 5000 parties
                    if total_parties % 5000 == 0:
                        print(f"   📈 Parties traitées : {total_parties:,} - {total_mains:,} mains extraites")
                        gc.collect()  # Nettoyage mémoire périodique

            # Stocker les séquences
            self.sequences = {
                'INDEX1': index1_seq,
                'INDEX2': index2_seq,
                'INDEX3': index3_seq,
                'INDEX5': index5_seq,  # INDEX5 : Combinaisons complètes
                'INDEX1_INDEX2': index1_index2_seq,
                'INDEX1_INDEX3': index1_index3_seq,
                'INDEX2_INDEX3': index2_index3_seq,
                'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
            }

            print(f"✅ Streaming terminé : {total_parties:,} parties, {total_mains:,} mains")
            print(f"   - INDEX1 : {len(index1_seq):,} valeurs")
            print(f"   - INDEX2 : {len(index2_seq):,} valeurs")
            print(f"   - INDEX3 : {len(index3_seq):,} valeurs")
            print(f"   - INDEX5 : {len(index5_seq):,} valeurs")
            print(f"   - Combinaisons : {len(index1_index2_seq):,} INDEX1_INDEX2")

            # Stocker le nombre de parties pour le rapport
            self.nb_parties_total = total_parties

            # Nettoyage final
            gc.collect()

        except Exception as e:
            print(f"❌ Erreur streaming : {e}")
            raise

    def _charger_standard(self):
        """Chargement standard pour fichiers plus petits"""
        gc.collect()

        with open(self.fichier_json, 'r', encoding='utf-8') as f:
            print("   📖 Parsing JSON standard...")
            self.donnees = json.load(f)

        nb_parties = len(self.donnees['parties'])
        print(f"✅ {nb_parties:,} parties chargées")

        # Stocker le nombre de parties pour le rapport
        self.nb_parties_total = nb_parties

        # Extraire les séquences
        self._extraire_sequences()

        # Libérer la mémoire du JSON original
        del self.donnees
        gc.collect()

    def _extraire_sequences(self):
        """Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties"""
        print("🔍 Extraction des séquences et combinaisons...")

        # Initialiser les listes de séquences individuelles
        index1_seq = []  # SYNC/DESYNC
        index2_seq = []  # pair_4/pair_6/impair_5
        index3_seq = []  # PLAYER/BANKER/TIE (garder TIE pour INDEX3 complet)
        index5_seq = []  # INDEX5 : Combinaison INDEX1_INDEX2_INDEX3 (18 combinaisons)

        # Initialiser les listes de séquences combinées
        index1_index2_seq = []  # SYNC/DESYNC + pair_4/pair_6/impair_5
        index1_index3_seq = []  # SYNC/DESYNC + PLAYER/BANKER
        index2_index3_seq = []  # pair_4/pair_6/impair_5 + PLAYER/BANKER
        index1_index2_index3_seq = []  # Combinaison des 3 INDEX

        total_mains = 0
        nb_parties = len(self.donnees['parties'])

        # Définir l'intervalle d'affichage selon le nombre de parties
        if nb_parties <= 1000:
            intervalle = 100
        elif nb_parties <= 10000:
            intervalle = 1000
        else:
            intervalle = 10000  # Pour 100 000 parties, affichage tous les 10 000

        print(f"📊 Traitement de {nb_parties:,} parties (affichage tous les {intervalle:,})...")

        for i, partie in enumerate(self.donnees['parties'], 1):
            for main in partie['mains']:
                # INDEX individuels
                idx1 = main['index1_sync_state']
                idx2 = main['index2_cards_category']
                idx3 = main['index3_result']

                index1_seq.append(idx1)
                index2_seq.append(idx2)
                index3_seq.append(idx3)  # Garder tous les résultats y compris TIE

                # INDEX5 : Combinaison complète INDEX1_INDEX2_INDEX3
                idx5 = main['index5_combined']
                index5_seq.append(idx5)

                # Combinaisons pour compatibilité
                if idx3 in ['PLAYER', 'BANKER']:
                    # Combinaisons avec INDEX3 (seulement si pas TIE)
                    index1_index3_seq.append(f"{idx1}_{idx3}")
                    index2_index3_seq.append(f"{idx2}_{idx3}")
                    index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                # Combinaisons sans INDEX3 (toujours possibles)
                index1_index2_seq.append(f"{idx1}_{idx2}")

                total_mains += 1

            # Affichage cyclique du progrès
            if i % intervalle == 0 or i == nb_parties:
                pourcentage = (i / nb_parties) * 100
                print(f"   📈 Parties traitées : {i:,}/{nb_parties:,} ({pourcentage:.1f}%) - {total_mains:,} mains extraites")

        self.sequences = {
            # INDEX individuels
            'INDEX1': index1_seq,
            'INDEX2': index2_seq,
            'INDEX3': index3_seq,
            'INDEX5': index5_seq,  # INDEX5 : Combinaison complète

            # Combinaisons d'INDEX (pour compatibilité)
            'INDEX1_INDEX2': index1_index2_seq,
            'INDEX1_INDEX3': index1_index3_seq,
            'INDEX2_INDEX3': index2_index3_seq,
            'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
        }

        print(f"✅ {total_mains:,} mains extraites")
        print(f"   - INDEX1 (SYNC/DESYNC) : {len(index1_seq):,} valeurs")
        print(f"   - INDEX2 (pair_4/6/impair_5) : {len(index2_seq):,} valeurs")
        print(f"   - INDEX3 (PLAYER/BANKER/TIE) : {len(index3_seq):,} valeurs")
        print(f"   - INDEX5 (Combinaisons complètes) : {len(index5_seq):,} valeurs")
        print(f"   - INDEX1_INDEX2 : {len(index1_index2_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX3 : {len(index1_index3_seq):,} combinaisons")
        print(f"   - INDEX2_INDEX3 : {len(index2_index3_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX2_INDEX3 : {len(index1_index2_index3_seq):,} combinaisons")

    def analyser_runs(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse les runs (séquences consécutives) d'une séquence

        Args:
            sequence: Liste des valeurs de la séquence
            nom_sequence: Nom de la séquence pour l'affichage

        Returns:
            dict: Résultats de l'analyse des runs
        """
        print(f"\n🔬 Analyse des runs : {nom_sequence}")

        # Identifier les runs
        runs = []
        if not sequence:
            return {}

        current_value = sequence[0]
        current_length = 1

        for i in range(1, len(sequence)):
            if sequence[i] == current_value:
                current_length += 1
            else:
                runs.append((current_value, current_length))
                current_value = sequence[i]
                current_length = 1

        # Ajouter le dernier run
        runs.append((current_value, current_length))

        # Analyser par type de valeur
        resultats = {}
        valeurs_uniques = list(set(sequence))

        for valeur in valeurs_uniques:
            longueurs_runs = [length for value, length in runs if value == valeur]

            if longueurs_runs:
                resultats[valeur] = self._analyser_runs_valeur(
                    longueurs_runs, valeur, len(sequence), sequence.count(valeur)
                )

        # Statistiques globales
        resultats['global'] = self._analyser_runs_global(runs, sequence)

        return resultats

    def _analyser_runs_valeur(self, longueurs: list, valeur: str, n_total: int, n_valeur: int) -> dict:
        """Analyse les runs pour une valeur spécifique"""

        # Statistiques descriptives
        stats_desc = {
            'nombre_runs': len(longueurs),
            'longueur_moyenne': np.mean(longueurs),
            'longueur_mediane': np.median(longueurs),
            'longueur_max': max(longueurs),
            'longueur_min': min(longueurs),
            'ecart_type': np.std(longueurs),
            'distribution': Counter(longueurs)
        }

        # Probabilité théorique
        p = n_valeur / n_total

        # Longueur moyenne théorique (distribution géométrique)
        longueur_moyenne_theorique = 1 / p if p > 0 else float('inf')

        # Nombre de runs théorique
        # Formule : E[R] ≈ 2np(1-p) + 1 pour le nombre total de runs
        # Pour une valeur spécifique : approximativement n_valeur / longueur_moyenne_theorique
        nombre_runs_theorique = n_valeur / longueur_moyenne_theorique if longueur_moyenne_theorique != float('inf') else 0

        # Tests statistiques
        tests = {}

        # Test de Kolmogorov-Smirnov contre distribution géométrique
        if p > 0 and p < 1:
            # Distribution géométrique théorique
            longueurs_max = max(longueurs)
            x_theorique = np.arange(1, longueurs_max + 1)
            cdf_theorique = geom.cdf(x_theorique, p)

            # CDF empirique
            longueurs_sorted = np.sort(longueurs)
            cdf_empirique = np.arange(1, len(longueurs) + 1) / len(longueurs)

            # Test KS (approximation)
            try:
                ks_stat, ks_pvalue = kstest(longueurs, lambda x: geom.cdf(x, p))
                tests['ks_test'] = {'statistic': ks_stat, 'p_value': ks_pvalue}
            except:
                tests['ks_test'] = {'statistic': None, 'p_value': None}

        return {
            'statistiques': stats_desc,
            'theorique': {
                'probabilite': p,
                'longueur_moyenne_theorique': longueur_moyenne_theorique,
                'nombre_runs_theorique': nombre_runs_theorique
            },
            'tests': tests,
            'ecarts': {
                'ecart_longueur_moyenne': stats_desc['longueur_moyenne'] - longueur_moyenne_theorique,
                'ecart_nombre_runs': stats_desc['nombre_runs'] - nombre_runs_theorique
            }
        }

    def _analyser_runs_global(self, runs: list, sequence: list) -> dict:
        """Analyse globale des runs"""

        # Nombre total de runs
        nombre_total_runs = len(runs)

        # Longueurs de tous les runs
        toutes_longueurs = [length for _, length in runs]

        # Statistiques globales
        stats_globales = {
            'nombre_total_runs': nombre_total_runs,
            'longueur_moyenne_globale': np.mean(toutes_longueurs),
            'longueur_max_globale': max(toutes_longueurs),
            'distribution_globale': Counter(toutes_longueurs)
        }

        # Runs Test (test de randomness)
        # Implémentation simplifiée du runs test
        n = len(sequence)
        valeurs_uniques = list(set(sequence))

        if len(valeurs_uniques) == 2:
            # Runs test pour séquence binaire
            n1 = sequence.count(valeurs_uniques[0])
            n2 = sequence.count(valeurs_uniques[1])

            # Nombre de runs observé
            R = nombre_total_runs

            # Moyenne et variance théoriques
            mu_R = (2 * n1 * n2) / (n1 + n2) + 1
            sigma2_R = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2)**2 * (n1 + n2 - 1))
            sigma_R = np.sqrt(sigma2_R)

            # Z-score
            if sigma_R > 0:
                z_score = (R - mu_R) / sigma_R
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            else:
                z_score = 0
                p_value = 1

            stats_globales['runs_test'] = {
                'runs_observes': R,
                'runs_attendus': mu_R,
                'z_score': z_score,
                'p_value': p_value,
                'significatif': p_value < 0.05
            }

        return stats_globales

    def calculer_autocorrelation(self, sequence: list, max_lag: int = 20) -> dict:
        """
        Calcule l'autocorrélation de la séquence

        Args:
            sequence: Séquence à analyser
            max_lag: Nombre maximum de lags à calculer

        Returns:
            dict: Coefficients d'autocorrélation
        """
        # Convertir en valeurs numériques pour l'autocorrélation
        valeurs_uniques = list(set(sequence))
        mapping = {val: i for i, val in enumerate(valeurs_uniques)}
        sequence_num = [mapping[val] for val in sequence]

        autocorr = {}
        n = len(sequence_num)

        for lag in range(1, min(max_lag + 1, n // 4)):
            # Calcul de l'autocorrélation pour le lag donné
            x1 = sequence_num[:-lag]
            x2 = sequence_num[lag:]

            if len(x1) > 0 and len(x2) > 0:
                corr = np.corrcoef(x1, x2)[0, 1]
                autocorr[lag] = corr if not np.isnan(corr) else 0

        return autocorr

    def calculer_entropie_shannon(self, sequence: list) -> float:
        """
        Calcule l'entropie de Shannon de la séquence

        Args:
            sequence: Séquence à analyser

        Returns:
            float: Entropie de Shannon
        """
        # Compter les fréquences
        compteur = Counter(sequence)
        n = len(sequence)

        # Calculer l'entropie
        entropie = 0
        for count in compteur.values():
            p = count / n
            if p > 0:
                entropie -= p * np.log2(p)

        return entropie

    def _calculer_entropie_locale(self, sequence: list, combinaison: str, taille_fenetre: int = 100) -> float:
        """
        Calcule l'entropie locale dans des fenêtres glissantes pour une combinaison donnée
        """
        if len(sequence) < taille_fenetre:
            return 0.0

        entropies_locales = []

        for i in range(len(sequence) - taille_fenetre + 1):
            fenetre = sequence[i:i + taille_fenetre]
            # Calculer la fréquence de la combinaison dans cette fenêtre
            freq_combo = fenetre.count(combinaison) / taille_fenetre

            if freq_combo > 0:
                # Entropie binaire : -p*log2(p) - (1-p)*log2(1-p)
                if freq_combo < 1:
                    entropie_bin = -(freq_combo * np.log2(freq_combo) +
                                   (1 - freq_combo) * np.log2(1 - freq_combo))
                else:
                    entropie_bin = 0
                entropies_locales.append(entropie_bin)

        return np.mean(entropies_locales) if entropies_locales else 0.0

    def _analyser_transitions_index5(self, sequence: list) -> dict:
        """
        Analyse les transitions entre combinaisons INDEX5 avec formules exactes
        """
        print("   🔄 Calcul des matrices de transition...")

        # Créer la matrice de transition
        combinaisons = sorted(set(sequence))
        n_combos = len(combinaisons)

        # Matrice de comptage des transitions
        matrice_transitions = np.zeros((n_combos, n_combos))

        for i in range(len(sequence) - 1):
            from_idx = combinaisons.index(sequence[i])
            to_idx = combinaisons.index(sequence[i + 1])
            matrice_transitions[from_idx, to_idx] += 1

        # Convertir en probabilités
        matrice_probabilities = np.zeros_like(matrice_transitions)
        for i in range(n_combos):
            row_sum = np.sum(matrice_transitions[i, :])
            if row_sum > 0:
                matrice_probabilities[i, :] = matrice_transitions[i, :] / row_sum

        # Calculer l'entropie de chaque ligne (diversité des transitions)
        entropies_transitions = []
        for i in range(n_combos):
            probs = matrice_probabilities[i, :]
            probs = probs[probs > 0]  # Supprimer les zéros
            if len(probs) > 0:
                entropie = -np.sum(probs * np.log2(probs))
                entropies_transitions.append(entropie)
            else:
                entropies_transitions.append(0)

        # Test d'indépendance chi-carré
        chi2_stat, p_value, dof, expected = chi2_contingency(matrice_transitions)

        # Coefficient de Gini pour mesurer la concentration des transitions
        gini_transitions = []
        for i in range(n_combos):
            row = matrice_transitions[i, :]
            if np.sum(row) > 0:
                gini_transitions.append(gini_coefficient(row))
            else:
                gini_transitions.append(0)

        return {
            'matrice_transitions': matrice_transitions.tolist(),
            'matrice_probabilities': matrice_probabilities.tolist(),
            'entropies_transitions': entropies_transitions,
            'gini_transitions': gini_transitions,
            'test_independance': {
                'chi2_statistic': chi2_stat,
                'p_value': p_value,
                'degrees_freedom': dof,
                'independant': p_value > 0.05
            },
            'combinaisons_ordre': combinaisons
        }

    def _analyser_patterns_temporels_index5(self, sequence: list) -> dict:
        """
        Analyse les patterns temporels dans INDEX5 avec formules exactes
        """
        print("   ⏰ Analyse des patterns temporels...")

        # 1. Analyse des cycles et périodicités
        cycles = self._detecter_cycles_index5(sequence)

        # 2. Analyse de la stationnarité (test de runs sur des segments)
        stationnarite = self._tester_stationnarite_index5(sequence)

        # 3. Analyse des tendances temporelles
        tendances = self._analyser_tendances_index5(sequence)

        return {
            'cycles': cycles,
            'stationnarite': stationnarite,
            'tendances': tendances
        }

    def _detecter_cycles_index5(self, sequence: list, max_periode: int = 50) -> dict:
        """
        Détecte les cycles dans la séquence INDEX5 en utilisant l'autocorrélation
        """
        # Convertir en séquence numérique
        combinaisons = sorted(set(sequence))
        sequence_num = [combinaisons.index(x) for x in sequence]

        # Calculer l'autocorrélation exacte
        autocorr = autocorrelation_function(sequence_num, max_lag=max_periode)

        # Détecter les pics d'autocorrélation (cycles potentiels)
        cycles_detectes = []
        for lag in range(2, len(autocorr)):
            if autocorr[lag] > 0.1:  # Seuil de corrélation significative
                cycles_detectes.append({
                    'periode': lag,
                    'correlation': autocorr[lag]
                })

        # Trier par corrélation décroissante
        cycles_detectes.sort(key=lambda x: x['correlation'], reverse=True)

        return {
            'cycles_detectes': cycles_detectes[:5],  # Top 5
            'autocorrelation_complete': autocorr.tolist()
        }

    def _tester_stationnarite_index5(self, sequence: list, nb_segments: int = 10) -> dict:
        """
        Teste la stationnarité de la séquence en comparant des segments
        """
        segment_size = len(sequence) // nb_segments
        if segment_size < 50:  # Segments trop petits
            return {'erreur': 'Séquence trop courte pour test de stationnarité'}

        # Diviser en segments
        segments = []
        for i in range(nb_segments):
            start = i * segment_size
            end = start + segment_size
            if end <= len(sequence):
                segments.append(sequence[start:end])

        # Calculer l'entropie de chaque segment
        entropies_segments = []
        for segment in segments:
            entropie = shannon_entropy_from_data(segment)
            entropies_segments.append(entropie)

        # Test de stationnarité : coefficient de variation des entropies
        cv_entropies = coefficient_of_variation(entropies_segments)

        # Test des runs sur les entropies
        entropies_binaires = [1 if e > np.median(entropies_segments) else 0
                             for e in entropies_segments]
        runs_entropies = runs_test(entropies_binaires)

        return {
            'entropies_segments': entropies_segments,
            'cv_entropies': cv_entropies,
            'runs_test_entropies': runs_entropies,
            'stationnaire': cv_entropies < 0.1 and runs_entropies['pvalue'] > 0.05
        }

    def _analyser_tendances_index5(self, sequence: list) -> dict:
        """
        Analyse les tendances temporelles dans la distribution des combinaisons
        """
        combinaisons = sorted(set(sequence))
        n_combos = len(combinaisons)

        # Diviser la séquence en 5 périodes
        n_periodes = 5
        taille_periode = len(sequence) // n_periodes

        tendances_par_combo = {}

        for combo in combinaisons:
            frequences_periodes = []

            for i in range(n_periodes):
                start = i * taille_periode
                end = start + taille_periode if i < n_periodes - 1 else len(sequence)
                periode = sequence[start:end]

                freq = periode.count(combo) / len(periode)
                frequences_periodes.append(freq)

            # Calculer la tendance (corrélation avec le temps)
            temps = list(range(n_periodes))
            if len(set(frequences_periodes)) > 1:  # Éviter division par zéro
                correlation_temps = np.corrcoef(temps, frequences_periodes)[0, 1]
            else:
                correlation_temps = 0

            # Coefficient de variation temporel
            cv_temporel = coefficient_of_variation(frequences_periodes)

            tendances_par_combo[combo] = {
                'frequences_periodes': frequences_periodes,
                'correlation_temps': correlation_temps,
                'cv_temporel': cv_temporel,
                'tendance': 'croissante' if correlation_temps > 0.3 else
                           'décroissante' if correlation_temps < -0.3 else 'stable'
            }

        return tendances_par_combo

    def analyser_index5_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX5 avec toutes les formules mathématiques exactes
        pour chacune des 18 combinaisons possibles
        """
        print("\n🔬 ANALYSE INDEX5 AVEC FORMULES MATHÉMATIQUES EXACTES")
        print("=" * 60)

        sequence_index5 = self.sequences['INDEX5']

        # 1. Identifier toutes les combinaisons INDEX5
        combinaisons_uniques = sorted(set(sequence_index5))
        print(f"📊 Combinaisons INDEX5 trouvées : {len(combinaisons_uniques)}")

        # 2. Analyse globale INDEX5
        print("\n🌐 ANALYSE GLOBALE INDEX5")
        print("-" * 30)

        # Entropie de Shannon exacte
        entropie_globale = shannon_entropy_from_data(sequence_index5)
        print(f"   Entropie de Shannon globale : {entropie_globale:.6f} bits")

        # Coefficient de Gini (pour mesurer l'inégalité de distribution)
        counts = [sequence_index5.count(combo) for combo in combinaisons_uniques]
        gini_global = gini_coefficient(counts)
        print(f"   Coefficient de Gini global : {gini_global:.6f}")

        # Coefficient de variation global
        cv_global = coefficient_of_variation(counts)
        print(f"   Coefficient de variation global : {cv_global:.6f}")

        # Autocorrélation exacte
        autocorr_exacte = autocorrelation_function(
            [combinaisons_uniques.index(x) for x in sequence_index5],
            max_lag=10
        )
        print(f"   Autocorrélation lag 1 : {autocorr_exacte[1]:.6f}")

        # Test des runs exact
        runs_result = runs_test(sequence_index5)
        print(f"   Test des runs p-value : {runs_result['pvalue']:.6f}")
        print(f"   Séquence aléatoire : {'Oui' if runs_result['pvalue'] > 0.05 else 'Non'}")

        # 3. Analyse détaillée par combinaison
        print("\n📋 ANALYSE DÉTAILLÉE PAR COMBINAISON INDEX5")
        print("-" * 45)

        resultats_par_combinaison = {}

        for i, combo in enumerate(combinaisons_uniques, 1):
            print(f"\n🎯 Combinaison {i:2d}/18 : {combo}")

            # Statistiques de base
            count = sequence_index5.count(combo)
            frequence = count / len(sequence_index5)
            print(f"   Occurrences : {count:,} ({frequence:.4f})")

            # Créer une séquence binaire pour cette combinaison
            sequence_binaire = [1 if x == combo else 0 for x in sequence_index5]

            # Analyse des runs pour cette combinaison spécifique
            runs_combo = runs_test(sequence_binaire)

            # Autocorrélation pour cette combinaison
            autocorr_combo = autocorrelation_function(sequence_binaire, max_lag=5)

            # Entropie locale (dans des fenêtres glissantes)
            entropie_locale = self._calculer_entropie_locale(sequence_index5, combo)

            # Stocker les résultats
            resultats_par_combinaison[combo] = {
                'occurrences': count,
                'frequence': frequence,
                'runs_test': {
                    'runs_observes': runs_combo['runs_observed'],
                    'runs_attendus': runs_combo['runs_expected'],
                    'p_value': runs_combo['pvalue'],
                    'aleatoire': runs_combo['pvalue'] > 0.05
                },
                'autocorrelation': {
                    'lag_1': autocorr_combo[1] if len(autocorr_combo) > 1 else 0,
                    'lag_2': autocorr_combo[2] if len(autocorr_combo) > 2 else 0,
                    'lag_3': autocorr_combo[3] if len(autocorr_combo) > 3 else 0
                },
                'entropie_locale': entropie_locale
            }

            print(f"   Runs p-value : {runs_combo['pvalue']:.6f}")
            print(f"   Autocorr lag 1 : {autocorr_combo[1] if len(autocorr_combo) > 1 else 0:.6f}")
            print(f"   Entropie locale : {entropie_locale:.6f}")

        # 4. Analyse des transitions entre combinaisons
        print("\n🔄 ANALYSE DES TRANSITIONS ENTRE COMBINAISONS")
        print("-" * 45)

        transitions = self._analyser_transitions_index5(sequence_index5)

        # 5. Analyse des patterns temporels
        print("\n⏰ ANALYSE DES PATTERNS TEMPORELS")
        print("-" * 35)

        patterns_temporels = self._analyser_patterns_temporels_index5(sequence_index5)

        # Stocker tous les résultats
        self.resultats['INDEX5_FORMULES_EXACTES'] = {
            'analyse_globale': {
                'entropie_shannon': entropie_globale,
                'coefficient_gini': gini_global,
                'coefficient_variation': cv_global,
                'autocorrelation': autocorr_exacte.tolist(),
                'runs_test': runs_result
            },
            'analyse_par_combinaison': resultats_par_combinaison,
            'transitions': transitions,
            'patterns_temporels': patterns_temporels,
            'combinaisons_trouvees': combinaisons_uniques,
            'nombre_combinaisons': len(combinaisons_uniques)
        }

        print(f"\n✅ Analyse INDEX5 avec formules exactes terminée")
        return self.resultats['INDEX5_FORMULES_EXACTES']

    def analyser_index2_index3_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX2_INDEX3 avec toutes les formules mathématiques exactes
        pour chacune des 9 combinaisons possibles (INDEX2 + INDEX3)

        Les 9 combinaisons possibles sont :
        - pair_4_BANKER, pair_4_PLAYER, pair_4_TIE
        - pair_6_BANKER, pair_6_PLAYER, pair_6_TIE
        - impair_5_BANKER, impair_5_PLAYER, impair_5_TIE
        """
        print("\n🔬 ANALYSE INDEX2_INDEX3 AVEC FORMULES MATHÉMATIQUES EXACTES")
        print("=" * 65)

        sequence_index2_index3 = self.sequences['INDEX2_INDEX3']

        # 1. Identifier toutes les combinaisons INDEX2_INDEX3
        combinaisons_uniques = sorted(set(sequence_index2_index3))
        print(f"📊 Combinaisons INDEX2_INDEX3 trouvées : {len(combinaisons_uniques)}")

        # Vérifier qu'on a bien les 9 combinaisons attendues
        combinaisons_attendues = [
            'pair_4_BANKER', 'pair_4_PLAYER', 'pair_4_TIE',
            'pair_6_BANKER', 'pair_6_PLAYER', 'pair_6_TIE',
            'impair_5_BANKER', 'impair_5_PLAYER', 'impair_5_TIE'
        ]

        print("📋 Combinaisons attendues vs trouvées :")
        for combo_attendue in combinaisons_attendues:
            if combo_attendue in combinaisons_uniques:
                count = sequence_index2_index3.count(combo_attendue)
                freq = count / len(sequence_index2_index3)
                print(f"   ✅ {combo_attendue} : {count:,} fois ({freq:.4f})")
            else:
                print(f"   ❌ {combo_attendue} : NON TROUVÉE")

        # 2. Analyse globale INDEX2_INDEX3
        print("\n🌐 ANALYSE GLOBALE INDEX2_INDEX3")
        print("-" * 35)

        # Entropie de Shannon exacte
        entropie_globale = shannon_entropy_from_data(sequence_index2_index3)
        print(f"   Entropie de Shannon globale : {entropie_globale:.6f} bits")

        # Coefficient de Gini (pour mesurer l'inégalité de distribution)
        counts = [sequence_index2_index3.count(combo) for combo in combinaisons_uniques]
        gini_global = gini_coefficient(counts)
        print(f"   Coefficient de Gini global : {gini_global:.6f}")

        # Coefficient de variation global
        cv_global = coefficient_of_variation(counts)
        print(f"   Coefficient de variation global : {cv_global:.6f}")

        # Autocorrélation exacte
        autocorr_exacte = autocorrelation_function(
            [combinaisons_uniques.index(x) for x in sequence_index2_index3],
            max_lag=10
        )
        print(f"   Autocorrélation lag 1 : {autocorr_exacte[1]:.6f}")

        # Test des runs exact
        runs_result = runs_test(sequence_index2_index3)
        print(f"   Test des runs p-value : {runs_result['pvalue']:.6f}")
        print(f"   Séquence aléatoire : {'Oui' if runs_result['pvalue'] > 0.05 else 'Non'}")

        # 3. Analyse détaillée par combinaison
        print("\n📋 ANALYSE DÉTAILLÉE PAR COMBINAISON INDEX2_INDEX3")
        print("-" * 50)

        resultats_par_combinaison = {}

        for i, combo in enumerate(combinaisons_uniques, 1):
            print(f"\n🎯 Combinaison {i:2d}/9 : {combo}")

            # Statistiques de base
            count = sequence_index2_index3.count(combo)
            frequence = count / len(sequence_index2_index3)
            print(f"   Occurrences : {count:,} ({frequence:.4f})")

            # Créer une séquence binaire pour cette combinaison
            sequence_binaire = [1 if x == combo else 0 for x in sequence_index2_index3]

            # Analyse des runs pour cette combinaison spécifique
            runs_combo = runs_test(sequence_binaire)

            # Autocorrélation pour cette combinaison
            autocorr_combo = autocorrelation_function(sequence_binaire, max_lag=5)

            # Entropie locale (dans des fenêtres glissantes)
            entropie_locale = self._calculer_entropie_locale(sequence_index2_index3, combo)

            # Stocker les résultats
            resultats_par_combinaison[combo] = {
                'occurrences': count,
                'frequence': frequence,
                'runs_test': {
                    'runs_observes': runs_combo['runs_observed'],
                    'runs_attendus': runs_combo['runs_expected'],
                    'p_value': runs_combo['pvalue'],
                    'aleatoire': runs_combo['pvalue'] > 0.05
                },
                'autocorrelation': {
                    'lag_1': autocorr_combo[1] if len(autocorr_combo) > 1 else 0,
                    'lag_2': autocorr_combo[2] if len(autocorr_combo) > 2 else 0,
                    'lag_3': autocorr_combo[3] if len(autocorr_combo) > 3 else 0
                },
                'entropie_locale': entropie_locale
            }

            print(f"   Runs p-value : {runs_combo['pvalue']:.6f}")
            print(f"   Autocorr lag 1 : {autocorr_combo[1] if len(autocorr_combo) > 1 else 0:.6f}")
            print(f"   Entropie locale : {entropie_locale:.6f}")

        # 4. Analyse des transitions entre combinaisons
        print("\n🔄 ANALYSE DES TRANSITIONS ENTRE COMBINAISONS INDEX2_INDEX3")
        print("-" * 55)

        transitions = self._analyser_transitions_index2_index3(sequence_index2_index3)

        # 5. Analyse des patterns temporels
        print("\n⏰ ANALYSE DES PATTERNS TEMPORELS INDEX2_INDEX3")
        print("-" * 45)

        patterns_temporels = self._analyser_patterns_temporels_index2_index3(sequence_index2_index3)

        # Stocker tous les résultats
        self.resultats['INDEX2_INDEX3_FORMULES_EXACTES'] = {
            'analyse_globale': {
                'entropie_shannon': entropie_globale,
                'coefficient_gini': gini_global,
                'coefficient_variation': cv_global,
                'autocorrelation': autocorr_exacte.tolist(),
                'runs_test': runs_result
            },
            'analyse_par_combinaison': resultats_par_combinaison,
            'transitions': transitions,
            'patterns_temporels': patterns_temporels,
            'combinaisons_trouvees': combinaisons_uniques,
            'nombre_combinaisons': len(combinaisons_uniques),
            'combinaisons_attendues': combinaisons_attendues
        }

        print(f"\n✅ Analyse INDEX2_INDEX3 avec formules exactes terminée")
        return self.resultats['INDEX2_INDEX3_FORMULES_EXACTES']

    def _analyser_transitions_index2_index3(self, sequence: list) -> dict:
        """
        Analyse les transitions entre combinaisons INDEX2_INDEX3 avec formules exactes
        """
        print("   🔄 Calcul des matrices de transition INDEX2_INDEX3...")

        # Créer la matrice de transition
        combinaisons = sorted(set(sequence))
        n_combos = len(combinaisons)

        # Matrice de comptage des transitions
        matrice_transitions = np.zeros((n_combos, n_combos))

        for i in range(len(sequence) - 1):
            from_idx = combinaisons.index(sequence[i])
            to_idx = combinaisons.index(sequence[i + 1])
            matrice_transitions[from_idx, to_idx] += 1

        # Convertir en probabilités
        matrice_probabilities = np.zeros_like(matrice_transitions)
        for i in range(n_combos):
            row_sum = np.sum(matrice_transitions[i, :])
            if row_sum > 0:
                matrice_probabilities[i, :] = matrice_transitions[i, :] / row_sum

        # Calculer l'entropie de chaque ligne (diversité des transitions)
        entropies_transitions = []
        for i in range(n_combos):
            probs = matrice_probabilities[i, :]
            probs = probs[probs > 0]  # Supprimer les zéros
            if len(probs) > 0:
                entropie = -np.sum(probs * np.log2(probs))
                entropies_transitions.append(entropie)
            else:
                entropies_transitions.append(0)

        # Test d'indépendance chi-carré
        chi2_stat, p_value, dof, expected = chi2_contingency(matrice_transitions)

        # Coefficient de Gini pour mesurer la concentration des transitions
        gini_transitions = []
        for i in range(n_combos):
            row = matrice_transitions[i, :]
            if np.sum(row) > 0:
                gini_transitions.append(gini_coefficient(row))
            else:
                gini_transitions.append(0)

        return {
            'matrice_transitions': matrice_transitions.tolist(),
            'matrice_probabilities': matrice_probabilities.tolist(),
            'entropies_transitions': entropies_transitions,
            'gini_transitions': gini_transitions,
            'test_independance': {
                'chi2_statistic': chi2_stat,
                'p_value': p_value,
                'degrees_freedom': dof,
                'independant': p_value > 0.05
            },
            'combinaisons_ordre': combinaisons
        }

    def _analyser_patterns_temporels_index2_index3(self, sequence: list) -> dict:
        """
        Analyse les patterns temporels dans INDEX2_INDEX3 avec formules exactes
        """
        print("   ⏰ Analyse des patterns temporels INDEX2_INDEX3...")

        # 1. Analyse des cycles et périodicités
        cycles = self._detecter_cycles_index2_index3(sequence)

        # 2. Analyse de la stationnarité (test de runs sur des segments)
        stationnarite = self._tester_stationnarite_index2_index3(sequence)

        # 3. Analyse des tendances temporelles
        tendances = self._analyser_tendances_index2_index3(sequence)

        return {
            'cycles': cycles,
            'stationnarite': stationnarite,
            'tendances': tendances
        }

    def _detecter_cycles_index2_index3(self, sequence: list, max_periode: int = 50) -> dict:
        """
        Détecte les cycles dans la séquence INDEX2_INDEX3 en utilisant l'autocorrélation
        """
        # Convertir en séquence numérique
        combinaisons = sorted(set(sequence))
        sequence_num = [combinaisons.index(x) for x in sequence]

        # Calculer l'autocorrélation exacte
        autocorr = autocorrelation_function(sequence_num, max_lag=max_periode)

        # Détecter les pics d'autocorrélation (cycles potentiels)
        cycles_detectes = []
        for lag in range(2, len(autocorr)):
            if autocorr[lag] > 0.1:  # Seuil de corrélation significative
                cycles_detectes.append({
                    'periode': lag,
                    'correlation': autocorr[lag]
                })

        # Trier par corrélation décroissante
        cycles_detectes.sort(key=lambda x: x['correlation'], reverse=True)

        return {
            'cycles_detectes': cycles_detectes[:5],  # Top 5
            'autocorrelation_complete': autocorr.tolist()
        }

    def _tester_stationnarite_index2_index3(self, sequence: list, nb_segments: int = 10) -> dict:
        """
        Teste la stationnarité de la séquence INDEX2_INDEX3 en comparant des segments
        """
        segment_size = len(sequence) // nb_segments
        if segment_size < 50:  # Segments trop petits
            return {'erreur': 'Séquence trop courte pour test de stationnarité'}

        # Diviser en segments
        segments = []
        for i in range(nb_segments):
            start = i * segment_size
            end = start + segment_size
            if end <= len(sequence):
                segments.append(sequence[start:end])

        # Calculer l'entropie de chaque segment
        entropies_segments = []
        for segment in segments:
            entropie = shannon_entropy_from_data(segment)
            entropies_segments.append(entropie)

        # Test de stationnarité : coefficient de variation des entropies
        cv_entropies = coefficient_of_variation(entropies_segments)

        # Test des runs sur les entropies
        entropies_binaires = [1 if e > np.median(entropies_segments) else 0
                             for e in entropies_segments]
        runs_entropies = runs_test(entropies_binaires)

        return {
            'entropies_segments': entropies_segments,
            'cv_entropies': cv_entropies,
            'runs_test_entropies': runs_entropies,
            'stationnaire': cv_entropies < 0.1 and runs_entropies['pvalue'] > 0.05
        }

    def _analyser_tendances_index2_index3(self, sequence: list) -> dict:
        """
        Analyse les tendances temporelles dans la distribution des combinaisons INDEX2_INDEX3
        """
        combinaisons = sorted(set(sequence))
        n_combos = len(combinaisons)

        # Diviser la séquence en 5 périodes
        n_periodes = 5
        taille_periode = len(sequence) // n_periodes

        tendances_par_combo = {}

        for combo in combinaisons:
            frequences_periodes = []

            for i in range(n_periodes):
                start = i * taille_periode
                end = start + taille_periode if i < n_periodes - 1 else len(sequence)
                periode = sequence[start:end]

                freq = periode.count(combo) / len(periode)
                frequences_periodes.append(freq)

            # Calculer la tendance (corrélation avec le temps)
            temps = list(range(n_periodes))
            if len(set(frequences_periodes)) > 1:  # Éviter division par zéro
                correlation_temps = np.corrcoef(temps, frequences_periodes)[0, 1]
            else:
                correlation_temps = 0

            # Coefficient de variation temporel
            cv_temporel = coefficient_of_variation(frequences_periodes)

            tendances_par_combo[combo] = {
                'frequences_periodes': frequences_periodes,
                'correlation_temps': correlation_temps,
                'cv_temporel': cv_temporel,
                'tendance': 'croissante' if correlation_temps > 0.3 else
                           'décroissante' if correlation_temps < -0.3 else 'stable'
            }

        return tendances_par_combo

    def analyser_toutes_sequences(self):
        """Lance l'analyse complète de toutes les séquences"""
        print("\nLANCEMENT DE L'ANALYSE COMPLÈTE")
        print("=" * 50)

        # 1. Analyse des runs (analyse originale)
        print("\nPHASE 1: ANALYSE DES RUNS")
        print("-" * 30)

        for nom_index, sequence in self.sequences.items():
            if nom_index in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:  # Analyser INDEX principaux + INDEX5
                print(f"\nAnalyse des runs {nom_index}")

                if nom_index == 'INDEX5':
                    print(f"   INDEX5 : Analyse des 18 combinaisons possibles")
                    # Afficher les combinaisons uniques trouvées
                    combinaisons_uniques = sorted(set(sequence))
                    print(f"   Combinaisons trouvées : {len(combinaisons_uniques)}")
                    for i, combo in enumerate(combinaisons_uniques, 1):
                        count = sequence.count(combo)
                        pourcentage = (count / len(sequence)) * 100
                        print(f"      {i:2d}. {combo} : {count:,} fois ({pourcentage:.2f}%)")

                # Analyse des runs
                resultats_runs = self.analyser_runs(sequence, nom_index)

                # Autocorrélation
                print(f"Calcul de l'autocorrélation...")
                autocorr = self.calculer_autocorrelation(sequence)

                # Entropie de Shannon
                print(f"Calcul de l'entropie de Shannon...")
                entropie = self.calculer_entropie_shannon(sequence)

                # Stocker les résultats
                self.resultats[nom_index] = {
                    'runs': resultats_runs,
                    'autocorrelation': autocorr,
                    'entropie_shannon': entropie,
                    'taille_sequence': len(sequence)
                }

                print(f"Analyse {nom_index} terminée")

        # 2. Analyse INDEX5 avec formules exactes
        print("\nPHASE 2: ANALYSE INDEX5 AVEC FORMULES EXACTES")
        print("-" * 50)

        self.analyser_index5_avec_formules_exactes()

        # 3. Analyse INDEX2_INDEX3 avec formules exactes
        print("\nPHASE 3: ANALYSE INDEX2_INDEX3 AVEC FORMULES EXACTES")
        print("-" * 55)

        self.analyser_index2_index3_avec_formules_exactes()

        print("\nAnalyse complète terminée")

    def generer_rapport(self, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des analyses

        Args:
            fichier_sortie: Nom du fichier de rapport (optionnel)
        """
        if not fichier_sortie:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fichier_sortie = f"rapport_analyse_sequences_{timestamp}.txt"

        print(f"\n📝 Génération du rapport : {fichier_sortie}")

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("RAPPORT D'ANALYSE STATISTIQUE DES SÉQUENCES LUPASCO\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date d'analyse : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Fichier source : {self.fichier_json}\n")
            f.write(f"Nombre de parties : {self.nb_parties_total:,}\n\n")

            # Rapport pour chaque index
            for nom_index, resultats in self.resultats.items():
                if nom_index == 'probabilites_conditionnelles':
                    continue  # Traité séparément plus bas

                f.write(f"\n{'='*20} ANALYSE {nom_index} {'='*20}\n\n")
                if 'taille_sequence' in resultats:
                    f.write(f"Taille de la séquence : {resultats['taille_sequence']:,} éléments\n")
                else:
                    f.write(f"Taille de la séquence : {len(self.sequences.get(nom_index, [])):,} éléments\n")
                if 'entropie_shannon' in resultats:
                    f.write(f"Entropie de Shannon : {resultats['entropie_shannon']:.4f} bits\n\n")
                else:
                    f.write("Entropie de Shannon : Non calculée\n\n")

                # Analyse des runs par valeur
                f.write("ANALYSE DES RUNS PAR VALEUR :\n")
                f.write("-" * 40 + "\n")

                if 'runs' in resultats and resultats['runs']:
                    for valeur, stats in resultats['runs'].items():
                        if valeur == 'global':
                            continue

                        f.write(f"\n🎯 {valeur} :\n")

                        # Statistiques descriptives
                        stats_desc = stats['statistiques']
                        f.write(f"   Nombre de runs : {stats_desc['nombre_runs']:,}\n")
                        f.write(f"   Longueur moyenne : {stats_desc['longueur_moyenne']:.2f}\n")
                        f.write(f"   Longueur médiane : {stats_desc['longueur_mediane']:.2f}\n")
                        f.write(f"   Longueur max : {stats_desc['longueur_max']}\n")
                        f.write(f"   Écart-type : {stats_desc['ecart_type']:.2f}\n")

                        # Comparaison théorique
                        theorique = stats['theorique']
                        f.write(f"   Probabilité : {theorique['probabilite']:.4f}\n")
                        f.write(f"   Longueur moyenne théorique : {theorique['longueur_moyenne_theorique']:.2f}\n")

                        # Écarts
                        ecarts = stats['ecarts']
                        f.write(f"   Écart longueur moyenne : {ecarts['ecart_longueur_moyenne']:.2f}\n")

                        # Tests statistiques
                        if 'ks_test' in stats['tests'] and stats['tests']['ks_test']['p_value']:
                            ks = stats['tests']['ks_test']
                            f.write(f"   Test KS p-value : {ks['p_value']:.4f}\n")
                            f.write(f"   Test KS significatif : {'Oui' if ks['p_value'] < 0.05 else 'Non'}\n")

                        # Distribution complète des longueurs
                        f.write("   Distribution complète des longueurs :\n")
                        dist_sorted = sorted(stats_desc['distribution'].items(), key=lambda x: x[0])  # Tri par longueur
                        for longueur, count in dist_sorted:
                            f.write(f"     Longueur {longueur} : {count:,} fois\n")

                    # Analyse globale
                    if 'global' in resultats['runs']:
                        global_stats = resultats['runs']['global']
                        f.write(f"\nANALYSE GLOBALE :\n")
                        f.write("-" * 20 + "\n")
                        f.write(f"Nombre total de runs : {global_stats['nombre_total_runs']:,}\n")
                        f.write(f"Longueur moyenne globale : {global_stats['longueur_moyenne_globale']:.2f}\n")
                        f.write(f"Longueur max globale : {global_stats['longueur_max_globale']}\n")

                        # Runs test
                        if 'runs_test' in global_stats:
                            rt = global_stats['runs_test']
                            f.write(f"\nRUNS TEST (Test de randomness) :\n")
                            f.write(f"   Runs observés : {rt['runs_observes']}\n")
                            f.write(f"   Runs attendus : {rt['runs_attendus']:.2f}\n")
                            f.write(f"   Z-score : {rt['z_score']:.4f}\n")
                            f.write(f"   P-value : {rt['p_value']:.4f}\n")
                            f.write(f"   Significatif : {'Oui' if rt['significatif'] else 'Non'}\n")
                else:
                    f.write("Aucune analyse des runs disponible\n")

                # Autocorrélation
                if 'autocorrelation' in resultats and resultats['autocorrelation']:
                    f.write(f"\nAUTOCORRÉLATION :\n")
                    f.write("-" * 15 + "\n")
                    autocorr = resultats['autocorrelation']
                    for lag in sorted(autocorr.keys())[:10]:  # Premiers 10 lags
                        f.write(f"   Lag {lag} : {autocorr[lag]:.4f}\n")
                else:
                    f.write(f"\nAUTOCORRÉLATION : Non calculée\n")

            # Rapport des probabilités conditionnelles
            if 'probabilites_conditionnelles' in self.resultats:
                f.write(f"\n{'='*60}\n")
                f.write("ANALYSE DES PROBABILITÉS CONDITIONNELLES\n")
                f.write("="*60 + "\n\n")

                probas_results = self.resultats['probabilites_conditionnelles']

                # INDEX2 en fonction d'INDEX1
                if 'index2_given_index1' in probas_results:
                    f.write("1. ANALYSE INDEX2 en fonction d'INDEX1\n")
                    f.write("-" * 40 + "\n")

                    i2_i1 = probas_results['index2_given_index1']
                    f.write(f"Test d'indépendance Chi² = {i2_i1['chi2_stat']:.4f}\n")
                    f.write(f"P-value = {i2_i1['p_value']:.6f}\n")
                    f.write(f"V de Cramér = {i2_i1['cramer_v']:.4f}\n")
                    f.write(f"Indépendance : {'Rejetée' if i2_i1['p_value'] < 0.05 else 'Acceptée'}\n\n")

                    f.write("Probabilités conditionnelles P(INDEX2|INDEX1) :\n")
                    for i1, probas in i2_i1['probabilites_conditionnelles'].items():
                        f.write(f"   {i1} :\n")
                        for i2, proba in probas.items():
                            f.write(f"      P({i2}|{i1}) = {proba:.4f}\n")
                        f.write("\n")

                # INDEX3 en fonction d'(INDEX1, INDEX2)
                if 'index3_given_index1_index2' in probas_results:
                    f.write("2. ANALYSE INDEX3 en fonction d'(INDEX1, INDEX2)\n")
                    f.write("-" * 45 + "\n")

                    i3_i1_i2 = probas_results['index3_given_index1_index2']
                    f.write("Probabilités conditionnelles P(INDEX3|INDEX1, INDEX2) :\n")

                    for i1, dict_i2 in i3_i1_i2['probabilites_conditionnelles'].items():
                        f.write(f"   {i1} :\n")
                        for i2, probas_i3 in dict_i2.items():
                            f.write(f"      {i2} :\n")
                            for i3, proba in probas_i3.items():
                                f.write(f"         P({i3}|{i1},{i2}) = {proba:.4f}\n")
                        f.write("\n")

                # Influences causales
                if 'influences_causales' in probas_results:
                    f.write("3. MESURES D'INFLUENCE CAUSALE\n")
                    f.write("-" * 30 + "\n")

                    influences = probas_results['influences_causales']

                    if 'information_mutuelle' in influences:
                        mi = influences['information_mutuelle']
                        f.write("Information mutuelle :\n")
                        f.write(f"   I(INDEX1; INDEX2) = {mi['index1_index2']:.4f} bits\n")
                        f.write(f"   I(INDEX2; INDEX3) = {mi['index2_index3']:.4f} bits\n\n")

                # Capacités prédictives
                if 'analyses_predictives' in probas_results:
                    f.write("4. CAPACITÉS PRÉDICTIVES\n")
                    f.write("-" * 25 + "\n")

                    pred = probas_results['analyses_predictives']

                    if 'prediction_index2' in pred:
                        p_i2 = pred['prediction_index2']
                        f.write(f"Prédiction INDEX2 à partir d'INDEX1 :\n")
                        f.write(f"   Précision = {p_i2['precision']:.4f}\n")
                        f.write(f"   Prédictions correctes = {p_i2['predictions_correctes']:,}\n")
                        f.write(f"   Total prédictions = {p_i2['total_predictions']:,}\n\n")

                    if 'prediction_index3' in pred:
                        p_i3 = pred['prediction_index3']
                        f.write(f"Prédiction INDEX3 à partir d'(INDEX1, INDEX2) :\n")
                        f.write(f"   Précision = {p_i3['precision']:.4f}\n")
                        f.write(f"   Prédictions correctes = {p_i3['predictions_correctes']:,}\n")
                        f.write(f"   Total prédictions = {p_i3['total_predictions']:,}\n\n")

            # Rapport des prédictions Lupasco
            if 'predictions_lupasco' in self.resultats:
                f.write(f"\n{'='*60}\n")
                f.write("ANALYSE PRÉDICTIVE LUPASCO\n")
                f.write("="*60 + "\n\n")

                pred_results = self.resultats['predictions_lupasco']

                # 1. Validation de la règle Lupasco
                if 'validation_lupasco' in pred_results:
                    f.write("1. VALIDATION DE LA RÈGLE LUPASCO\n")
                    f.write("-" * 35 + "\n")

                    validation = pred_results['validation_lupasco']
                    f.write(f"Précision globale de la règle : {validation['precision_globale']:.4f}\n")
                    f.write(f"Transitions correctes : {validation['transitions_correctes']:,}\n")
                    f.write(f"Transitions incorrectes : {validation['transitions_incorrectes']:,}\n")
                    f.write(f"Total transitions : {validation['total_transitions']:,}\n\n")

                    f.write("Détails par INDEX2 :\n")
                    for index2_val, details in validation['details_par_index2'].items():
                        f.write(f"   {index2_val} :\n")
                        f.write(f"      Précision : {details['precision']:.4f}\n")
                        f.write(f"      Perpétue : {details['perpetue']:,} ({details['prob_perpetue']:.4f})\n")
                        f.write(f"      Alterne : {details['alterne']:,} ({details['prob_alterne']:.4f})\n")
                        f.write(f"      Total : {details['total']:,}\n\n")

                    f.write("Probabilités conditionnelles P(INDEX1(t+1)|INDEX2(t)) :\n")
                    for index2_val, probas in validation['probabilites_conditionnelles'].items():
                        f.write(f"   {index2_val} :\n")
                        for index1_val, proba in probas.items():
                            f.write(f"      P(INDEX1(t+1)={index1_val}|INDEX2(t)={index2_val}) = {proba:.4f}\n")
                        f.write("\n")

                # 2. Prédictions INDEX2
                if 'prediction_index2' in pred_results:
                    f.write("2. PRÉDICTIONS INDEX2 SYNCHRONES\n")
                    f.write("-" * 30 + "\n")

                    pred_i2 = pred_results['prediction_index2']
                    f.write("Prédictions INDEX2_most_likely :\n")
                    for i1, pred in pred_i2['predictions'].items():
                        f.write(f"   Si INDEX1={i1} → INDEX2_most_likely={pred['most_likely']} (p={pred['probabilite']:.4f})\n")
                    f.write("\n")

                # 3. Prédictions INDEX3
                if 'prediction_index3' in pred_results:
                    f.write("3. PRÉDICTIONS FINALES INDEX3\n")
                    f.write("-" * 25 + "\n")

                    pred_i3 = pred_results['prediction_index3']
                    f.write(f"Précision prédictive globale : {pred_i3['precision_globale']:.4f}\n")
                    f.write(f"Prédictions correctes : {pred_i3['predictions_correctes']:,}\n")
                    f.write(f"Total prédictions : {pred_i3['total_predictions']:,}\n\n")

                    f.write("Exemples de prédictions INDEX3 :\n")
                    count = 0
                    for i1 in pred_i3['predictions']:
                        for i2 in pred_i3['predictions'][i1]:
                            if count < 12:  # Afficher 12 exemples dans le rapport
                                pred = pred_i3['predictions'][i1][i2]
                                f.write(f"   Si INDEX1={i1}, INDEX2={i2} → INDEX3={pred['most_likely']} (p={pred['probabilite']:.4f})\n")
                                count += 1
                    f.write("\n")

            # Rapport INDEX5 avec formules exactes
            if 'INDEX5_FORMULES_EXACTES' in self.resultats:
                f.write(f"\n{'='*60}\n")
                f.write("ANALYSE INDEX5 AVEC FORMULES MATHÉMATIQUES EXACTES\n")
                f.write("="*60 + "\n\n")

                index5_results = self.resultats['INDEX5_FORMULES_EXACTES']

                # 1. Analyse globale
                f.write("1. ANALYSE GLOBALE INDEX5\n")
                f.write("-" * 25 + "\n")

                globale = index5_results['analyse_globale']
                f.write(f"Nombre de combinaisons trouvées : {index5_results['nombre_combinaisons']}/18\n")
                f.write(f"Entropie de Shannon globale : {globale['entropie_shannon']:.6f} bits\n")
                f.write(f"Coefficient de Gini global : {globale['coefficient_gini']:.6f}\n")
                f.write(f"Coefficient de variation global : {globale['coefficient_variation']:.6f}\n")
                f.write(f"Autocorrélation lag 1 : {globale['autocorrelation'][1]:.6f}\n")
                f.write(f"Test des runs p-value : {globale['runs_test']['pvalue']:.6f}\n")
                f.write(f"Séquence aléatoire : {'Oui' if globale['runs_test']['pvalue'] > 0.05 else 'Non'}\n\n")

                # 2. Analyse par combinaison
                f.write("2. ANALYSE DÉTAILLÉE PAR COMBINAISON\n")
                f.write("-" * 35 + "\n")

                par_combo = index5_results['analyse_par_combinaison']
                for i, (combo, stats) in enumerate(par_combo.items(), 1):
                    f.write(f"\n🎯 Combinaison {i:2d}/18 : {combo}\n")
                    f.write(f"   Occurrences : {stats['occurrences']:,} ({stats['frequence']:.4f})\n")
                    f.write(f"   Runs observés : {stats['runs_test']['runs_observes']}\n")
                    f.write(f"   Runs attendus : {stats['runs_test']['runs_attendus']:.2f}\n")
                    f.write(f"   Runs p-value : {stats['runs_test']['p_value']:.6f}\n")
                    f.write(f"   Aléatoire : {'Oui' if stats['runs_test']['aleatoire'] else 'Non'}\n")
                    f.write(f"   Autocorrélation lag 1 : {stats['autocorrelation']['lag_1']:.6f}\n")
                    f.write(f"   Autocorrélation lag 2 : {stats['autocorrelation']['lag_2']:.6f}\n")
                    f.write(f"   Autocorrélation lag 3 : {stats['autocorrelation']['lag_3']:.6f}\n")
                    f.write(f"   Entropie locale : {stats['entropie_locale']:.6f}\n")

                # 3. Analyse des transitions
                f.write("\n3. ANALYSE DES TRANSITIONS\n")
                f.write("-" * 25 + "\n")

                transitions = index5_results['transitions']
                f.write(f"Test d'indépendance Chi² : {transitions['test_independance']['chi2_statistic']:.4f}\n")
                f.write(f"P-value : {transitions['test_independance']['p_value']:.6f}\n")
                f.write(f"Transitions indépendantes : {'Oui' if transitions['test_independance']['independant'] else 'Non'}\n")
                f.write(f"Entropie moyenne des transitions : {np.mean(transitions['entropies_transitions']):.4f}\n")
                f.write(f"Gini moyen des transitions : {np.mean(transitions['gini_transitions']):.4f}\n\n")

                # 4. Patterns temporels
                f.write("4. PATTERNS TEMPORELS\n")
                f.write("-" * 20 + "\n")

                patterns = index5_results['patterns_temporels']

                # Cycles détectés
                if 'cycles' in patterns and patterns['cycles']['cycles_detectes']:
                    f.write("Cycles détectés :\n")
                    for cycle in patterns['cycles']['cycles_detectes'][:3]:  # Top 3
                        f.write(f"   Période {cycle['periode']} : corrélation {cycle['correlation']:.4f}\n")
                else:
                    f.write("Aucun cycle significatif détecté\n")

                # Stationnarité
                if 'stationnarite' in patterns and 'stationnaire' in patterns['stationnarite']:
                    statio = patterns['stationnarite']
                    f.write(f"Stationnarité : {'Oui' if statio['stationnaire'] else 'Non'}\n")
                    f.write(f"CV des entropies : {statio['cv_entropies']:.4f}\n")

                # Tendances
                if 'tendances' in patterns:
                    f.write("\nTendances par combinaison :\n")
                    for combo, tendance in list(patterns['tendances'].items())[:5]:  # Top 5
                        f.write(f"   {combo} : {tendance['tendance']} (corr: {tendance['correlation_temps']:.4f})\n")

                f.write("\n")

            # Rapport INDEX2_INDEX3 avec formules exactes
            if 'INDEX2_INDEX3_FORMULES_EXACTES' in self.resultats:
                f.write(f"\n{'='*65}\n")
                f.write("ANALYSE INDEX2_INDEX3 AVEC FORMULES MATHÉMATIQUES EXACTES\n")
                f.write("="*65 + "\n\n")

                index2_index3_results = self.resultats['INDEX2_INDEX3_FORMULES_EXACTES']

                # 1. Analyse globale
                f.write("1. ANALYSE GLOBALE INDEX2_INDEX3\n")
                f.write("-" * 30 + "\n")

                globale = index2_index3_results['analyse_globale']
                f.write(f"Nombre de combinaisons trouvées : {index2_index3_results['nombre_combinaisons']}/9\n")
                f.write(f"Entropie de Shannon globale : {globale['entropie_shannon']:.6f} bits\n")
                f.write(f"Coefficient de Gini global : {globale['coefficient_gini']:.6f}\n")
                f.write(f"Coefficient de variation global : {globale['coefficient_variation']:.6f}\n")
                f.write(f"Autocorrélation lag 1 : {globale['autocorrelation'][1]:.6f}\n")
                f.write(f"Test des runs p-value : {globale['runs_test']['pvalue']:.6f}\n")
                f.write(f"Séquence aléatoire : {'Oui' if globale['runs_test']['pvalue'] > 0.05 else 'Non'}\n\n")

                # 2. Analyse par combinaison
                f.write("2. ANALYSE DÉTAILLÉE PAR COMBINAISON INDEX2_INDEX3\n")
                f.write("-" * 45 + "\n")

                par_combo = index2_index3_results['analyse_par_combinaison']
                for i, (combo, stats) in enumerate(par_combo.items(), 1):
                    f.write(f"\n🎯 Combinaison {i:2d}/9 : {combo}\n")
                    f.write(f"   Occurrences : {stats['occurrences']:,} ({stats['frequence']:.4f})\n")
                    f.write(f"   Runs observés : {stats['runs_test']['runs_observes']}\n")
                    f.write(f"   Runs attendus : {stats['runs_test']['runs_attendus']:.2f}\n")
                    f.write(f"   Runs p-value : {stats['runs_test']['p_value']:.6f}\n")
                    f.write(f"   Aléatoire : {'Oui' if stats['runs_test']['aleatoire'] else 'Non'}\n")
                    f.write(f"   Autocorrélation lag 1 : {stats['autocorrelation']['lag_1']:.6f}\n")
                    f.write(f"   Autocorrélation lag 2 : {stats['autocorrelation']['lag_2']:.6f}\n")
                    f.write(f"   Autocorrélation lag 3 : {stats['autocorrelation']['lag_3']:.6f}\n")
                    f.write(f"   Entropie locale : {stats['entropie_locale']:.6f}\n")

                # 3. Analyse des transitions
                f.write("\n3. ANALYSE DES TRANSITIONS INDEX2_INDEX3\n")
                f.write("-" * 35 + "\n")

                transitions = index2_index3_results['transitions']
                f.write(f"Test d'indépendance Chi² : {transitions['test_independance']['chi2_statistic']:.4f}\n")
                f.write(f"P-value : {transitions['test_independance']['p_value']:.6f}\n")
                f.write(f"Transitions indépendantes : {'Oui' if transitions['test_independance']['independant'] else 'Non'}\n")
                f.write(f"Entropie moyenne des transitions : {np.mean(transitions['entropies_transitions']):.4f}\n")
                f.write(f"Gini moyen des transitions : {np.mean(transitions['gini_transitions']):.4f}\n\n")

                # 4. Patterns temporels
                f.write("4. PATTERNS TEMPORELS INDEX2_INDEX3\n")
                f.write("-" * 30 + "\n")

                patterns = index2_index3_results['patterns_temporels']

                # Cycles détectés
                if 'cycles' in patterns and patterns['cycles']['cycles_detectes']:
                    f.write("Cycles détectés :\n")
                    for cycle in patterns['cycles']['cycles_detectes'][:3]:  # Top 3
                        f.write(f"   Période {cycle['periode']} : corrélation {cycle['correlation']:.4f}\n")
                else:
                    f.write("Aucun cycle significatif détecté\n")

                # Stationnarité
                if 'stationnarite' in patterns and 'stationnaire' in patterns['stationnarite']:
                    statio = patterns['stationnarite']
                    f.write(f"Stationnarité : {'Oui' if statio['stationnaire'] else 'Non'}\n")
                    f.write(f"CV des entropies : {statio['cv_entropies']:.4f}\n")

                # Tendances
                if 'tendances' in patterns:
                    f.write("\nTendances par combinaison INDEX2_INDEX3 :\n")
                    for combo, tendance in list(patterns['tendances'].items())[:9]:  # Toutes les 9
                        f.write(f"   {combo} : {tendance['tendance']} (corr: {tendance['correlation_temps']:.4f})\n")

                f.write("\n")

                # 5. Comparaison avec les combinaisons attendues
                f.write("5. VALIDATION DES COMBINAISONS ATTENDUES\n")
                f.write("-" * 35 + "\n")

                combinaisons_attendues = index2_index3_results['combinaisons_attendues']
                combinaisons_trouvees = index2_index3_results['combinaisons_trouvees']

                f.write("Combinaisons INDEX2_INDEX3 attendues vs trouvées :\n")
                for combo_attendue in combinaisons_attendues:
                    if combo_attendue in combinaisons_trouvees:
                        if combo_attendue in par_combo:
                            stats = par_combo[combo_attendue]
                            f.write(f"   ✅ {combo_attendue} : {stats['occurrences']:,} fois ({stats['frequence']:.4f})\n")
                        else:
                            f.write(f"   ✅ {combo_attendue} : TROUVÉE (stats non disponibles)\n")
                    else:
                        f.write(f"   ❌ {combo_attendue} : NON TROUVÉE\n")

                f.write("\n")

        print(f"Rapport généré : {fichier_sortie}")
        return fichier_sortie


def main():
    """Fonction principale du programme"""
    import sys

    print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO - VERSION SIMPLIFIÉE")
    print("=" * 70)
    print("Analyse des runs, entropie, autocorrélation et génération de rapport")
    print()

    # Vérifier les arguments
    if len(sys.argv) < 2:
        print("❌ Erreur : Fichier JSON requis")
        print("\nUsage:")
        print("  python analyseur.py <fichier_json>")
        print("\nExemple:")
        print("  python analyseur.py dataset_baccarat_lupasco_20250617_232800.json")
        sys.exit(1)

    fichier_json = sys.argv[1]

    try:
        # Créer l'analyseur
        analyseur = AnalyseurSequencesLupasco(fichier_json)

        # Charger les données
        analyseur.charger_donnees()

        # Lancer l'analyse complète
        analyseur.analyser_toutes_sequences()

        # Générer le rapport
        fichier_rapport = analyseur.generer_rapport()

        print(f"\n🎉 ANALYSE TERMINÉE AVEC SUCCÈS !")
        print(f"📝 Rapport détaillé : {fichier_rapport}")

    except Exception as e:
        print(f"\n❌ ERREUR : {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
