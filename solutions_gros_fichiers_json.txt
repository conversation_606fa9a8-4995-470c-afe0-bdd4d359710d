SOLUTIONS POUR LIRE DES FICHIERS JSON DE PLUSIEURS GB EN PYTHON
================================================================

Date: 2025-06-17
Contexte: Fichier JSON de 7 GB à analyser pour le système Lupasco
Problème: json.load() standard ne peut pas charger 7 GB en mémoire

## 1. SOLUTIONS DE STREAMING JSON

### 1.1 ijson - Parser JSON Streaming (RECOMMANDÉ)
- **Installation**: `pip install ijson`
- **Avantage**: Parse le JSON par chunks sans charger tout en mémoire
- **Usage**: Idéal pour gros fichiers JSON structurés
- **Performance**: Utilise moins de mémoire, plus lent que parsing standard
- **Code exemple**:
```python
import ijson

with open('large_file.json', 'rb') as f:
    # Parser streaming des éléments d'un array
    items = ijson.items(f, 'parties.item')
    for item in items:
        # Traiter chaque élément individuellement
        process_item(item)
```

### 1.2 json-stream - Alternative à ijson
- **Installation**: `pip install json-stream`
- **Avantage**: API plus simple que ijson
- **Usage**: Streaming parser avec interface intuitive

## 2. PARSERS JSON HAUTE PERFORMANCE

### 2.1 orjson - Parser JSON Ultra-Rapide
- **Installation**: `pip install orjson`
- **Avantage**: 2-3x plus rapide que json standard, écrit en Rust
- **Inconvénient**: Charge toujours tout en mémoire
- **Usage**: Bon pour fichiers moyens (< 2 GB) avec besoin de vitesse

### 2.2 ujson - Parser JSON Rapide
- **Installation**: `pip install ujson`
- **Avantage**: Plus rapide que json standard
- **Inconvénient**: Charge tout en mémoire

### 2.3 simdjson - Parser SIMD
- **Installation**: `pip install pysimdjson`
- **Avantage**: Utilise instructions SIMD pour vitesse extrême
- **Inconvénient**: Charge tout en mémoire

## 3. STRATÉGIES DE GESTION MÉMOIRE

### 3.1 Chunked Processing
- Diviser le fichier en plusieurs petits fichiers
- Traiter chaque chunk séparément
- Combiner les résultats

### 3.2 Memory Mapping
- Utiliser `mmap` pour mapper le fichier en mémoire virtuelle
- Permet d'accéder au fichier sans le charger entièrement

### 3.3 Garbage Collection Agressif
```python
import gc
gc.collect()  # Forcer le nettoyage mémoire
```

## 4. SOLUTIONS SPÉCIFIQUES POUR NOTRE CAS

### 4.1 Approche Streaming avec ijson (RECOMMANDÉE)
```python
import ijson
import gc

def charger_avec_streaming(fichier_json):
    sequences = {'INDEX1': [], 'INDEX2': [], 'INDEX3': []}
    total_parties = 0
    
    with open(fichier_json, 'rb') as f:
        parties = ijson.items(f, 'parties.item')
        
        for partie in parties:
            for main in partie['mains']:
                sequences['INDEX1'].append(main['index1_sync_state'])
                sequences['INDEX2'].append(main['index2_cards_category'])
                sequences['INDEX3'].append(main['index3_result'])
            
            total_parties += 1
            if total_parties % 1000 == 0:
                print(f"Parties traitées: {total_parties}")
                gc.collect()
    
    return sequences
```

### 4.2 Approche par Batches
- Lire le fichier par petits groupes de parties
- Traiter chaque batch séparément
- Éviter de stocker toutes les données simultanément

### 4.3 Conversion en Format Plus Efficace
- Convertir le JSON en format binaire (pickle, parquet, HDF5)
- Utiliser ces formats pour analyses futures

## 5. OUTILS EXTERNES

### 5.1 jq - Outil en ligne de commande
- Préprocesser le fichier JSON avec jq
- Extraire seulement les données nécessaires

### 5.2 Apache Spark / Dask
- Pour traitement distribué de très gros fichiers
- Overkill pour notre cas mais option disponible

## 6. RECOMMANDATIONS POUR NOTRE PROJET

### Approche 1: ijson Streaming (PRIORITÉ 1)
1. Installer ijson: `pip install ijson`
2. Modifier le programme pour utiliser ijson.items()
3. Traiter les parties une par une
4. Affichage de progression tous les 1000 parties

### Approche 2: Chunked Processing (PRIORITÉ 2)
1. Diviser le fichier JSON en plusieurs petits fichiers
2. Traiter chaque fichier séparément
3. Combiner les résultats d'analyse

### Approche 3: Conversion de Format (PRIORITÉ 3)
1. Convertir le JSON en format Parquet ou HDF5
2. Utiliser pandas/dask pour l'analyse
3. Plus efficace pour analyses répétées

## 7. CODE D'IMPLÉMENTATION RECOMMANDÉ

```python
import ijson
import gc
from collections import defaultdict

class AnalyseurStreamingLupasco:
    def __init__(self, fichier_json):
        self.fichier_json = fichier_json
        self.sequences = defaultdict(list)
    
    def charger_avec_streaming(self):
        print("🔄 Chargement streaming...")
        total_parties = 0
        total_mains = 0
        
        with open(self.fichier_json, 'rb') as f:
            parties = ijson.items(f, 'parties.item')
            
            for partie in parties:
                for main in partie['mains']:
                    self.sequences['INDEX1'].append(main['index1_sync_state'])
                    self.sequences['INDEX2'].append(main['index2_cards_category'])
                    if main['index3_result'] in ['PLAYER', 'BANKER']:
                        self.sequences['INDEX3'].append(main['index3_result'])
                    total_mains += 1
                
                total_parties += 1
                
                # Progression et nettoyage mémoire
                if total_parties % 5000 == 0:
                    print(f"   📈 {total_parties:,} parties, {total_mains:,} mains")
                    gc.collect()
        
        print(f"✅ Streaming terminé: {total_parties:,} parties")
        return self.sequences
```

## 8. PERFORMANCE ATTENDUE

- **ijson**: ~10-20 minutes pour 7 GB (dépend du CPU)
- **Mémoire**: ~100-500 MB au lieu de 7+ GB
- **Fiabilité**: Très haute, pas de risque de crash mémoire

## 9. INSTALLATION REQUISE

```bash
pip install ijson
```

Cette solution permettra de traiter le fichier de 7 GB sans problème de mémoire
et avec un suivi de progression en temps réel.
