ANALYSE DES ÉCARTS-TYPES INDEX5 - 18 COMBINAISONS
==================================================

TABLEAU COMPLET DES ÉCARTS-TYPES
---------------------------------

COMBINAISON                   | ÉCART-TYPE | CATÉGORIE
------------------------------|------------|------------------
SYNC_impair_5_TIE             | 0.00       | DÉTERMINISTE
DESYNC_impair_5_TIE           | 0.00       | DÉTERMINISTE
SYNC_impair_5_PLAYER          | 0.03       | QUASI-DÉTERMINISTE
SYNC_impair_5_BANKER          | 0.03       | QUASI-DÉTERMINISTE
DESYNC_impair_5_PLAYER        | 0.04       | QUASI-DÉTERMINISTE
DESYNC_impair_5_BANKER        | 0.04       | QUASI-DÉTERMINISTE
SYNC_pair_4_TIE               | 0.19       | FAIBLE VARIABILITÉ
SYNC_pair_6_TIE               | 0.19       | FAIBLE VARIABILITÉ
DESYNC_pair_4_TIE             | 0.20       | FAIBLE VARIABILITÉ
DESYNC_pair_6_TIE             | 0.19       | FAIBLE VARIABILITÉ
SYNC_pair_6_BANKER            | 0.41       | VARIABILITÉ MODÉRÉE
DESYNC_pair_6_BANKER          | 0.41       | VARIABILITÉ MODÉRÉE
DESYNC_pair_6_PLAYER          | 0.46       | VARIABILITÉ MODÉRÉE
SYNC_pair_6_PLAYER            | 0.46       | VARIABILITÉ MODÉRÉE
DESYNC_pair_4_PLAYER          | 0.50       | VARIABILITÉ ÉLEVÉE
SYNC_pair_4_PLAYER            | 0.50       | VARIABILITÉ ÉLEVÉE
SYNC_pair_4_BANKER            | 0.50       | VARIABILITÉ ÉLEVÉE
DESYNC_pair_4_BANKER          | 0.50       | VARIABILITÉ ÉLEVÉE

INTERPRÉTATION PAR CATÉGORIE
=============================

1. DÉTERMINISME ABSOLU (σ = 0.00)
----------------------------------
Combinaisons : SYNC_impair_5_TIE, DESYNC_impair_5_TIE

Signification :
- 100% des runs font longueur 1
- Aucune variabilité = comportement parfaitement prévisible
- TIE + impair_5 = JAMAIS de séquences consécutives
- Pattern : TIE apparaît toujours isolé quand combiné avec impair_5

2. QUASI-DÉTERMINISME (σ = 0.03-0.04)
--------------------------------------
Combinaisons : Toutes les impair_5 + PLAYER/BANKER

Signification :
- 99.87-99.9% des runs font longueur 1
- Variabilité quasi-nulle mais pas totalement absente
- impair_5 impose une forte contrainte d'isolement
- Quelques rares runs de longueur 2 (0.1-0.13%)

3. FAIBLE VARIABILITÉ (σ = 0.19-0.20)
--------------------------------------
Combinaisons : Toutes les pair_4/pair_6 + TIE

Signification :
- 96-97% des runs font longueur 1
- TIE reste majoritairement isolé même avec pair_4/pair_6
- Légère possibilité de runs de longueur 2-4
- TIE = événement naturellement "disruptif"

4. VARIABILITÉ MODÉRÉE (σ = 0.41-0.46)
---------------------------------------
Combinaisons : pair_6 + PLAYER/BANKER

Signification :
- 85-87% des runs font longueur 1
- Possibilité notable de runs plus longs (jusqu'à 7-8)
- pair_6 permet plus de persistance que pair_4
- PLAYER/BANKER plus "groupables" que TIE

5. VARIABILITÉ ÉLEVÉE (σ = 0.50)
---------------------------------
Combinaisons : pair_4 + PLAYER/BANKER

Signification :
- 82-83% des runs font longueur 1
- Plus forte probabilité de séquences (jusqu'à 8)
- pair_4 = configuration la plus "persistante"
- PLAYER/BANKER équilibrés en termes de groupement

PATTERNS STRUCTURELS RÉVÉLÉS
=============================

HIÉRARCHIE DE PERSISTANCE :
---------------------------
1. impair_5 → Isolation forcée (σ ≈ 0.00-0.04)
2. TIE → Isolation naturelle (σ ≈ 0.19-0.20)
3. pair_6 → Persistance modérée (σ ≈ 0.41-0.46)
4. pair_4 → Persistance maximale (σ = 0.50)

RÈGLES LUPASCO CONFIRMÉES :
---------------------------
- impair_5 = "Disrupteur absolu" → Force l'alternance
- pair_4/pair_6 = "Stabilisateurs" → Permettent la persistance
- TIE = "Événement rare isolé" → Naturellement non-groupable

SYMÉTRIES PARFAITES :
---------------------
- SYNC ≈ DESYNC : Écarts-types identiques par paire
- PLAYER ≈ BANKER : Comportements symétriques
- Seul TIE se distingue par sa rareté et son isolement

CONCLUSION STATISTIQUE
======================

L'écart-type révèle une HIÉRARCHIE STRICTE dans le système Lupasco :

- INDEX2 (pair/impair) = Facteur déterminant de la variabilité
- INDEX3 (résultat) = Modulateur secondaire
- INDEX1 (SYNC/DESYNC) = Neutre (pas d'impact sur σ)

Cette analyse confirme que le système Lupasco possède des RÈGLES INTERNES 
STRICTES qui gouvernent la formation des séquences.

IMPLICATIONS PRATIQUES
======================

1. PRÉDICTIBILITÉ MAXIMALE :
   - Combinaisons impair_5 + TIE : 100% prévisibles (toujours isolées)
   - Combinaisons impair_5 + PLAYER/BANKER : 99.9% prévisibles

2. ZONES D'INCERTITUDE :
   - Combinaisons pair_4 + PLAYER/BANKER : Plus grande variabilité
   - Possibilité de runs jusqu'à 8 occurrences consécutives

3. STRATÉGIES OPTIMALES :
   - Exploiter la prévisibilité des combinaisons impair_5
   - Anticiper l'isolement systématique des TIE
   - Surveiller les patterns de persistance avec pair_4/pair_6

Date d'analyse : 2025-06-18
Source : Analyse statistique INDEX5 - Dataset Lupasco 100,000 parties
