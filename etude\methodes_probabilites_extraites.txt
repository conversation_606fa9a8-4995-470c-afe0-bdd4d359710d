MÉTHODES EXTRAITES - PROBABILITÉS CONDITIONNELLES LUPASCO
============================================================

Date d'extraction : 2025-06-18
Source : ../analyseur_sequences_lupasco.py
Nombre de méthodes : 13


================================================================================
MÉTHODE 1/13 : analyser_probabilites_conditionnelles
================================================================================
Ligne : 510
Pattern trouvé : probabilites_conditionnelles
Définition : def analyser_probabilites_conditionnelles(self):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def analyser_probabilites_conditionnelles(self):
        """
        Analyse complète des probabilités conditionnelles selon les spécifications Lupasco
        """
        print("\nANALYSE DES PROBABILITÉS CONDITIONNELLES")
        print("=" * 50)

        # Extraire les données individuelles (sans combinaisons pour cette analyse)
        index1_data = self.sequences['INDEX1']
        index2_data = self.sequences['INDEX2']
        index3_data = self.sequences['INDEX3']

        # Créer les données alignées (même longueur)
        min_length = min(len(index1_data), len(index2_data), len(index3_data))
        index1_aligned = index1_data[:min_length]
        index2_aligned = index2_data[:min_length]
        index3_aligned = index3_data[:min_length]

        resultats = {}

        # 1. Analyse INDEX2 en fonction de INDEX1
        print("\n1. ANALYSE INDEX2 en fonction de INDEX1")
        resultats['index2_given_index1'] = self._analyser_index2_given_index1(
            index1_aligned, index2_aligned
        )

        # 2. Analyse INDEX3 en fonction de (INDEX1, INDEX2)
        print("\n2. ANALYSE INDEX3 en fonction de (INDEX1, INDEX2)")
        resultats['index3_given_index1_index2'] = self._analyser_index3_given_index1_index2(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 3. Mesures d'influence causale
        print("\n3. MESURES D'INFLUENCE CAUSALE")
        resultats['influences_causales'] = self._analyser_influences_causales(
            index1_aligned, index2_aligned, index3_aligned
        )

        # 4. Analyses prédictives
        print("\n4. ANALYSES PRÉDICTIVES")
        resultats['analyses_predictives'] = self._analyser_capacites_predictives(
            index1_aligned, index2_aligned, index3_aligned
        )

        return resultats


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 2/13 : _entrainer_modeles_cloisonnes
================================================================================
Ligne : 596
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _entrainer_modeles_cloisonnes(self, index1_data, index2_data, index3_data):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _entrainer_modeles_cloisonnes(self, index1_data, index2_data, index3_data):
        """
        Entraînement des modèles avec cloisonnement temporel strict
        Utilise seulement les données historiques pour apprendre
        """
        print("   Entraînement avec cloisonnement temporel...")

        # Créer historique d'entraînement (toutes les mains sauf la dernière)
        historique_entrainement = []
        for i in range(len(index1_data) - 1):  # Exclure la dernière main
            historique_entrainement.append({
                'INDEX1': index1_data[i],
                'INDEX2': index2_data[i],
                'INDEX3': index3_data[i]
            })

        print(f"      Taille historique d'entraînement : {len(historique_entrainement):,} mains")

        # 1. Modèle P(INDEX2|INDEX1) - synchrone
        modele_index2 = self._entrainer_modele_index2(historique_entrainement)

        # 2. Modèle P(INDEX3|INDEX1, INDEX2) - synchrone
        modele_index3 = self._entrainer_modele_index3(historique_entrainement)

        return {
            'modele_index2': modele_index2,
            'modele_index3': modele_index3,
            'taille_entrainement': len(historique_entrainement)
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 3/13 : _entrainer_modele_index2
================================================================================
Ligne : 626
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _entrainer_modele_index2(self, historique):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _entrainer_modele_index2(self, historique):
        """
        Entraîner P(INDEX2|INDEX1) sur données historiques uniquement
        """
        print("      Entraînement modèle INDEX2...")

        from collections import defaultdict
        contingence_index2 = defaultdict(lambda: defaultdict(int))

        # Compter occurrences dans l'historique
        for main in historique:
            i1 = main['INDEX1']
            i2 = main['INDEX2']
            contingence_index2[i1][i2] += 1

        # Calculer probabilités P(INDEX2|INDEX1)
        probabilites_index2 = {}
        for i1 in contingence_index2:
            total_i1 = sum(contingence_index2[i1].values())
            probabilites_index2[i1] = {}
            for i2 in contingence_index2[i1]:
                probabilites_index2[i1][i2] = contingence_index2[i1][i2] / total_i1

        # Identifier INDEX2_most_likely pour chaque INDEX1
        predictions_index2 = {}
        for i1 in probabilites_index2:
            probas = probabilites_index2[i1]
            most_likely = max(probas.items(), key=lambda x: x[1])
            predictions_index2[i1] = {
                'most_likely': most_likely[0],
                'probabilite': most_likely[1],
                'toutes_probas': probas
            }

        print(f"         Modèle INDEX2 entraîné sur {sum(sum(d.values()) for d in contingence_index2.values()):,} exemples")

        return {
            'probabilites': probabilites_index2,
            'predictions': predictions_index2,
            'contingence': dict(contingence_index2)
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 4/13 : _entrainer_modele_index3
================================================================================
Ligne : 668
Pattern trouvé : P\(INDEX3.*\|.*INDEX1.*INDEX2.*\)
Définition : def _entrainer_modele_index3(self, historique):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _entrainer_modele_index3(self, historique):
        """
        Entraîner P(INDEX3|INDEX1, INDEX2) sur données historiques uniquement
        """
        print("      Entraînement modèle INDEX3...")

        from collections import defaultdict
        contingence_index3 = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        # Compter occurrences dans l'historique
        for main in historique:
            i1 = main['INDEX1']
            i2 = main['INDEX2']
            i3 = main['INDEX3']
            contingence_index3[i1][i2][i3] += 1

        # Calculer probabilités P(INDEX3|INDEX1, INDEX2)
        probabilites_index3 = {}
        predictions_index3 = {}

        for i1 in contingence_index3:
            probabilites_index3[i1] = {}
            predictions_index3[i1] = {}

            for i2 in contingence_index3[i1]:
                total_i1_i2 = sum(contingence_index3[i1][i2].values())
                probabilites_index3[i1][i2] = {}

                # Calculer probabilités pour chaque INDEX3
                for i3 in contingence_index3[i1][i2]:
                    probabilites_index3[i1][i2][i3] = contingence_index3[i1][i2][i3] / total_i1_i2

                # Identifier INDEX3_most_likely
                if contingence_index3[i1][i2]:
                    probas_i3 = probabilites_index3[i1][i2]
                    most_likely = max(probas_i3.items(), key=lambda x: x[1])
                    predictions_index3[i1][i2] = {
                        'most_likely': most_likely[0],
                        'probabilite': most_likely[1],
                        'toutes_probas': probas_i3
                    }

        total_exemples = sum(sum(sum(d2.values()) for d2 in d1.values()) for d1 in contingence_index3.values())
        print(f"         Modèle INDEX3 entraîné sur {total_exemples:,} exemples")

        return {
            'probabilites': probabilites_index3,
            'predictions': predictions_index3,
            'contingence': dict(contingence_index3)
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 5/13 : _predire_main_cloisonnee
================================================================================
Ligne : 844
Pattern trouvé : def.*predire.*index[23]
Définition : def _predire_main_cloisonnee(self, index1_actuel, modele_index2, modele_index3):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _predire_main_cloisonnee(self, index1_actuel, modele_index2, modele_index3):
        """
        Prédiction cloisonnée : accès seulement à INDEX1 de la main actuelle
        """
        # Étape 1 : Prédire INDEX2 avec seulement INDEX1
        if index1_actuel in modele_index2['predictions']:
            index2_prediction = modele_index2['predictions'][index1_actuel]
            index2_predit = index2_prediction['most_likely']

            # Étape 2 : Prédire INDEX3 avec INDEX1 + INDEX2 prédit
            if (index1_actuel in modele_index3['predictions'] and
                index2_predit in modele_index3['predictions'][index1_actuel]):

                index3_prediction = modele_index3['predictions'][index1_actuel][index2_predit]
                index3_predit = index3_prediction['most_likely']

                return {
                    'INDEX2_prediction': index2_predit,
                    'INDEX2_probabilite': index2_prediction['probabilite'],
                    'INDEX3_prediction': index3_predit,
                    'INDEX3_probabilite': index3_prediction['probabilite']
                }

        return None


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 6/13 : _verifier_contraintes_probabilites
================================================================================
Ligne : 1265
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _verifier_contraintes_probabilites(self, probabilites_temporelles):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _verifier_contraintes_probabilites(self, probabilites_temporelles):
        """Vérifications mathématiques selon formules optimales"""
        verifications = {}

        # Contrainte : Σ P(INDEX1(t+1)|INDEX2(t)) = 1 pour chaque INDEX2(t)
        for i2_t, probas in probabilites_temporelles.items():
            somme = sum(probas.values())
            verifications[f"somme_P(INDEX1|{i2_t})"] = {
                'somme': somme,
                'valide': abs(somme - 1.0) < 1e-10
            }

        return verifications


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 7/13 : _predire_index2_given_index1_synchrone
================================================================================
Ligne : 1279
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _predire_index2_given_index1_synchrone(self, index1_data, index2_data):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _predire_index2_given_index1_synchrone(self, index1_data, index2_data):
        """
        Calcul de P(INDEX2(t+1)|INDEX1(t+1)) selon formules Stanford optimales
        """
        print("   Calcul P(INDEX2(t+1)|INDEX1(t+1)) selon formules académiques...")

        # Créer tableau de contingence synchrone selon Stanford
        from collections import defaultdict
        contingence_synchrone = defaultdict(lambda: defaultdict(int))

        for i1, i2 in zip(index1_data, index2_data):
            contingence_synchrone[i1][i2] += 1

        # Calculer probabilités conditionnelles selon Stanford
        # P(INDEX2(t+1)|INDEX1(t+1)) = count(INDEX2(t+1) ET INDEX1(t+1)) / count(INDEX1(t+1))
        probas_conditionnelles_synchrones = {}
        totaux_index1 = {}

        # Calculer totaux pour chaque INDEX1(t+1)
        for i1 in contingence_synchrone:
            totaux_index1[i1] = sum(contingence_synchrone[i1].values())

        # Appliquer formule Stanford
        for i1 in contingence_synchrone:
            probas_conditionnelles_synchrones[i1] = {}
            for i2 in contingence_synchrone[i1]:
                probas_conditionnelles_synchrones[i1][i2] = contingence_synchrone[i1][i2] / totaux_index1[i1]

        # Identification INDEX2_most_likely selon formules optimales
        # INDEX2_most_likely(i1) = argmax{P(pair_4|i1), P(pair_6|i1), P(impair_5|i1)}
        predictions_index2 = {}
        for i1 in probas_conditionnelles_synchrones:
            probas = probas_conditionnelles_synchrones[i1]
            most_likely = max(probas.items(), key=lambda x: x[1])
            predictions_index2[i1] = {
                'most_likely': most_likely[0],
                'probabilite': most_likely[1],
                'toutes_probas': probas
            }

        # Tests statistiques synchrones selon formules optimales
        chi2_synchrone, p_value_synchrone, v_cramer_synchrone = self._tests_statistiques_synchrones(contingence_synchrone)

        # Vérifications contraintes probabilités
        verifications_synchrones = self._verifier_contraintes_synchrones(probas_conditionnelles_synchrones)

        print(f"      Prédictions INDEX2 calculées pour {len(predictions_index2)} états INDEX1")
        print(f"      Chi² synchrone : {chi2_synchrone:.4f}, p-value : {p_value_synchrone:.6f}")
        print(f"      V de Cramér synchrone : {v_cramer_synchrone:.4f}")

        for i1, pred in predictions_index2.items():
            print(f"      Si INDEX1={i1} → INDEX2_most_likely={pred['most_likely']} (p={pred['probabilite']:.4f})")

        return {
            'probabilites_conditionnelles_synchrones': probas_conditionnelles_synchrones,
            'predictions_index2': predictions_index2,
            'contingence_synchrone': dict(contingence_synchrone),
            'chi2_synchrone': chi2_synchrone,
            'p_value_synchrone': p_value_synchrone,
            'v_cramer_synchrone': v_cramer_synchrone,
            'verifications_synchrones': verifications_synchrones
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 8/13 : _verifier_contraintes_synchrones
================================================================================
Ligne : 1384
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _verifier_contraintes_synchrones(self, probas_conditionnelles_synchrones):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _verifier_contraintes_synchrones(self, probas_conditionnelles_synchrones):
        """Vérifications contraintes P(INDEX2|INDEX1)"""
        verifications = {}

        # Contrainte : Σ P(INDEX2(t+1)|INDEX1(t+1)) = 1 pour chaque INDEX1(t+1)
        for i1, probas in probas_conditionnelles_synchrones.items():
            somme = sum(probas.values())
            verifications[f"somme_P(INDEX2|{i1})"] = {
                'somme': somme,
                'valide': abs(somme - 1.0) < 1e-10
            }

        return verifications


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 9/13 : _predire_index3_given_index1_index2
================================================================================
Ligne : 1398
Pattern trouvé : P\(INDEX3.*\|.*INDEX1.*INDEX2.*\)
Définition : def _predire_index3_given_index1_index2(self, index1_data, index2_data, index3_data):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _predire_index3_given_index1_index2(self, index1_data, index2_data, index3_data):
        """
        Prédiction finale INDEX3 selon formules Stanford optimales
        P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) avec métriques Paris-Saclay
        """
        print("   Calcul P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) selon formules académiques...")

        # Créer tableau de contingence 3D selon Stanford
        from collections import defaultdict
        contingence_3d = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_3d[i1][i2][i3] += 1

        # Calculer probabilités conditionnelles selon Stanford
        # P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) = count(INDEX3 ET INDEX1 ET INDEX2) / count(INDEX1 ET INDEX2)
        probas_conditionnelles_finales = {}
        predictions_index3 = {}

        for i1 in contingence_3d:
            probas_conditionnelles_finales[i1] = {}
            predictions_index3[i1] = {}

            for i2 in contingence_3d[i1]:
                total_i1_i2 = sum(contingence_3d[i1][i2].values())
                probas_conditionnelles_finales[i1][i2] = {}

                # Appliquer formule Stanford pour chaque INDEX3
                for i3 in contingence_3d[i1][i2]:
                    probas_conditionnelles_finales[i1][i2][i3] = contingence_3d[i1][i2][i3] / total_i1_i2

                # Identification INDEX3_most_likely selon formules optimales
                # INDEX3_most_likely(i1, i2) = argmax{P(PLAYER|i1,i2), P(BANKER|i1,i2), P(TIE|i1,i2)}
                if contingence_3d[i1][i2]:
                    probas_i3 = probas_conditionnelles_finales[i1][i2]
                    most_likely = max(probas_i3.items(), key=lambda x: x[1])
                    predictions_index3[i1][i2] = {
                        'most_likely': most_likely[0],
                        'probabilite': most_likely[1],
                        'toutes_probas': probas_i3
                    }

        # Métriques de performance selon Paris-Saclay
        metriques_performance = self._calculer_metriques_performance(
            index1_data, index2_data, index3_data, predictions_index3
        )

        # Matrice de confusion selon formules optimales
        matrice_confusion = self._calculer_matrice_confusion(
            index1_data, index2_data, index3_data, predictions_index3
        )

        # V de Cramér performance selon Real Statistics
        v_cramer_performance = self._calculer_v_cramer_performance(
            index1_data, index2_data, index3_data, predictions_index3
        )

        # Vérifications contraintes probabilités finales
        verifications_finales = self._verifier_contraintes_finales(probas_conditionnelles_finales)

        print(f"      Précision globale : {metriques_performance['precision_globale']:.4f}")
        print(f"      F1-Score moyen : {metriques_performance['f1_score_moyen']:.4f}")
        print(f"      V de Cramér performance : {v_cramer_performance:.4f}")
        print(f"      Prédictions correctes : {metriques_performance['predictions_correctes']:,}/{metriques_performance['total_predictions']:,}")

        # Afficher exemples de prédictions avec probabilités
        print("      Exemples de prédictions INDEX3 :")
        count = 0
        for i1 in predictions_index3:
            for i2 in predictions_index3[i1]:
                if count < 6:
                    pred = predictions_index3[i1][i2]
                    print(f"         Si INDEX1={i1}, INDEX2={i2} → INDEX3={pred['most_likely']} (p={pred['probabilite']:.4f})")
                    count += 1

        return {
            'probabilites_conditionnelles_finales': probas_conditionnelles_finales,
            'predictions_index3': predictions_index3,
            'metriques_performance': metriques_performance,
            'matrice_confusion': matrice_confusion,
            'v_cramer_performance': v_cramer_performance,
            'verifications_finales': verifications_finales,
            'contingence_3d': dict(contingence_3d)
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 10/13 : _verifier_contraintes_finales
================================================================================
Ligne : 1604
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _verifier_contraintes_finales(self, probas_conditionnelles_finales):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _verifier_contraintes_finales(self, probas_conditionnelles_finales):
        """Vérifications contraintes P(INDEX3|INDEX1,INDEX2)"""
        verifications = {}

        # Contrainte : Σ P(INDEX3(t+1)|INDEX1(t+1), INDEX2(t+1)) = 1
        for i1, dict_i2 in probas_conditionnelles_finales.items():
            for i2, probas in dict_i2.items():
                somme = sum(probas.values())
                verifications[f"somme_P(INDEX3|{i1},{i2})"] = {
                    'somme': somme,
                    'valide': abs(somme - 1.0) < 1e-10
                }

        return verifications


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 11/13 : _analyser_index2_given_index1
================================================================================
Ligne : 1619
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _analyser_index2_given_index1(self, index1_data, index2_data):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _analyser_index2_given_index1(self, index1_data, index2_data):
        """Analyse P(INDEX2|INDEX1)"""
        print("   Calcul des probabilités conditionnelles P(INDEX2|INDEX1)...")

        # Créer tableau de contingence
        from collections import defaultdict
        contingence = defaultdict(lambda: defaultdict(int))

        for i1, i2 in zip(index1_data, index2_data):
            contingence[i1][i2] += 1

        # Calculer probabilités conditionnelles
        probas_conditionnelles = {}
        totaux_index1 = {}

        # Calculer totaux pour chaque valeur d'INDEX1
        for i1 in contingence:
            totaux_index1[i1] = sum(contingence[i1].values())

        # Calculer P(INDEX2|INDEX1)
        for i1 in contingence:
            probas_conditionnelles[i1] = {}
            for i2 in contingence[i1]:
                probas_conditionnelles[i1][i2] = contingence[i1][i2] / totaux_index1[i1]

        # Créer matrice pour test chi2
        valeurs_i1 = sorted(contingence.keys())
        valeurs_i2 = sorted(set(i2 for i1_dict in contingence.values() for i2 in i1_dict.keys()))

        matrice_contingence = []
        for i1 in valeurs_i1:
            ligne = []
            for i2 in valeurs_i2:
                ligne.append(contingence[i1].get(i2, 0))
            matrice_contingence.append(ligne)

        # Test chi2 d'indépendance
        chi2_stat, p_value, dof, expected = stats.chi2_contingency(matrice_contingence)

        # V de Cramér
        n = sum(sum(ligne) for ligne in matrice_contingence)
        cramer_v = np.sqrt(chi2_stat / (n * (min(len(valeurs_i1), len(valeurs_i2)) - 1)))

        print(f"      Chi2 = {chi2_stat:.4f}, p-value = {p_value:.6f}")
        print(f"      V de Cramér = {cramer_v:.4f}")

        return {
            'probabilites_conditionnelles': probas_conditionnelles,
            'contingence': dict(contingence),
            'chi2_stat': chi2_stat,
            'p_value': p_value,
            'cramer_v': cramer_v,
            'valeurs_index1': valeurs_i1,
            'valeurs_index2': valeurs_i2,
            'matrice_contingence': matrice_contingence
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 12/13 : _analyser_index3_given_index1_index2
================================================================================
Ligne : 1676
Pattern trouvé : P\(INDEX3.*\|.*INDEX1.*INDEX2.*\)
Définition : def _analyser_index3_given_index1_index2(self, index1_data, index2_data, index3_data):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _analyser_index3_given_index1_index2(self, index1_data, index2_data, index3_data):
        """Analyse P(INDEX3|INDEX1, INDEX2)"""
        print("   Calcul des probabilités conditionnelles P(INDEX3|INDEX1, INDEX2)...")

        # Créer tableau de contingence 3D
        from collections import defaultdict
        contingence_3d = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_3d[i1][i2][i3] += 1

        # Calculer probabilités conditionnelles P(INDEX3|INDEX1, INDEX2)
        probas_conditionnelles = {}

        for i1 in contingence_3d:
            probas_conditionnelles[i1] = {}
            for i2 in contingence_3d[i1]:
                total_i1_i2 = sum(contingence_3d[i1][i2].values())
                probas_conditionnelles[i1][i2] = {}
                for i3 in contingence_3d[i1][i2]:
                    probas_conditionnelles[i1][i2][i3] = contingence_3d[i1][i2][i3] / total_i1_i2

        # Tests chi2 pour chaque combinaison INDEX1-INDEX2
        tests_chi2 = {}

        for i1 in contingence_3d:
            for i2 in contingence_3d[i1]:
                if len(contingence_3d[i1][i2]) > 1:  # Au moins 2 valeurs d'INDEX3
                    # Test si INDEX3 suit distribution uniforme pour cette combinaison
                    observes = list(contingence_3d[i1][i2].values())
                    total = sum(observes)
                    attendus = [total / len(observes)] * len(observes)

                    if all(a >= 5 for a in attendus):  # Condition pour chi2
                        chi2_stat = sum((o - a)**2 / a for o, a in zip(observes, attendus))
                        p_value = 1 - stats.chi2.cdf(chi2_stat, len(observes) - 1)
                        tests_chi2[f"{i1}_{i2}"] = {
                            'chi2_stat': chi2_stat,
                            'p_value': p_value,
                            'observes': observes,
                            'attendus': attendus
                        }

        print(f"      Calculé {len(tests_chi2)} tests chi2 pour combinaisons INDEX1-INDEX2")

        return {
            'probabilites_conditionnelles': probas_conditionnelles,
            'contingence_3d': dict(contingence_3d),
            'tests_chi2': tests_chi2
        }


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------


================================================================================
MÉTHODE 13/13 : _analyser_capacites_predictives
================================================================================
Ligne : 1842
Pattern trouvé : P\(INDEX2.*\|.*INDEX1.*\)
Définition : def _analyser_capacites_predictives(self, index1_data, index2_data, index3_data):

------------------------------------------------------------
CODE COMPLET :
------------------------------------------------------------

    def _analyser_capacites_predictives(self, index1_data, index2_data, index3_data):
        """Analyse des capacités prédictives du système"""
        print("   Calcul des capacités prédictives...")

        capacites = {}

        # 1. Prédiction INDEX2 à partir d'INDEX1
        print("      Prédiction INDEX2 à partir d'INDEX1...")

        # Calculer argmax(P(INDEX2|INDEX1))
        contingence_i1_i2 = defaultdict(lambda: defaultdict(int))
        for i1, i2 in zip(index1_data, index2_data):
            contingence_i1_i2[i1][i2] += 1

        predictions_i2 = {}
        for i1 in contingence_i1_i2:
            total_i1 = sum(contingence_i1_i2[i1].values())
            probas = {i2: count/total_i1 for i2, count in contingence_i1_i2[i1].items()}
            prediction = max(probas.items(), key=lambda x: x[1])
            predictions_i2[i1] = {
                'prediction': prediction[0],
                'probabilite': prediction[1],
                'toutes_probas': probas
            }

        # Calculer précision de prédiction
        predictions_correctes_i2 = 0
        total_predictions_i2 = 0

        for i1, i2_reel in zip(index1_data, index2_data):
            if i1 in predictions_i2:
                prediction = predictions_i2[i1]['prediction']
                if prediction == i2_reel:
                    predictions_correctes_i2 += 1
                total_predictions_i2 += 1

        precision_i2 = predictions_correctes_i2 / total_predictions_i2 if total_predictions_i2 > 0 else 0

        capacites['prediction_index2'] = {
            'predictions': predictions_i2,
            'precision': precision_i2,
            'predictions_correctes': predictions_correctes_i2,
            'total_predictions': total_predictions_i2
        }

        # 2. Prédiction INDEX3 à partir d'(INDEX1, INDEX2)
        print("      Prédiction INDEX3 à partir d'(INDEX1, INDEX2)...")

        contingence_i1_i2_i3 = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        for i1, i2, i3 in zip(index1_data, index2_data, index3_data):
            contingence_i1_i2_i3[i1][i2][i3] += 1

        predictions_i3 = {}
        for i1 in contingence_i1_i2_i3:
            predictions_i3[i1] = {}
            for i2 in contingence_i1_i2_i3[i1]:
                total_i1_i2 = sum(contingence_i1_i2_i3[i1][i2].values())
                if total_i1_i2 > 0:
                    probas = {i3: count/total_i1_i2 for i3, count in contingence_i1_i2_i3[i1][i2].items()}
                    prediction = max(probas.items(), key=lambda x: x[1])
                    predictions_i3[i1][i2] = {
                        'prediction': prediction[0],
                        'probabilite': prediction[1],
                        'toutes_probas': probas
                    }

        # Calculer précision de prédiction INDEX3
        predictions_correctes_i3 = 0
        total_predictions_i3 = 0

        for i1, i2, i3_reel in zip(index1_data, index2_data, index3_data):
            if i1 in predictions_i3 and i2 in predictions_i3[i1]:
                prediction = predictions_i3[i1][i2]['prediction']
                if prediction == i3_reel:
                    predictions_correctes_i3 += 1
                total_predictions_i3 += 1

        precision_i3 = predictions_correctes_i3 / total_predictions_i3 if total_predictions_i3 > 0 else 0

        capacites['prediction_index3'] = {
            'predictions': predictions_i3,
            'precision': precision_i3,
            'predictions_correctes': predictions_correctes_i3,
            'total_predictions': total_predictions_i3
        }

        print(f"      Précision prédiction INDEX2 : {precision_i2:.4f}")
        print(f"      Précision prédiction INDEX3 : {precision_i3:.4f}")

        return capacites


------------------------------------------------------------
FIN DE MÉTHODE
------------------------------------------------------------

