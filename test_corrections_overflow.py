# -*- coding: utf-8 -*-
"""
TEST DES CORRECTIONS OVERFLOW ET ERREURS DE CLÉS
===============================================

Script pour tester que les corrections d'overflow et d'erreurs de clés fonctionnent.

Usage:
    python test_corrections_overflow.py

Auteur: Assistant IA Augment
Date: 18 juin 2025
"""

import sys
import os
import json
import numpy as np
from datetime import datetime

# Ajouter le répertoire parent au path pour importer les modules
sys.path.append('.')
sys.path.append('..')

# Importer les modules corrigés
from formules_mathematiques_exactes import runs_test, shannon_entropy_from_data
from etude.analyseur import AnalyseurSequencesLupasco

def test_runs_test_overflow():
    """
    Teste le test des runs avec des cas qui causaient des overflows
    """
    print("🧪 TEST DES CORRECTIONS OVERFLOW DANS RUNS_TEST")
    print("=" * 50)
    
    # Test 1: Séquence très longue (cas d'overflow)
    print("\n1. Test avec séquence très longue (100,000 éléments)")
    sequence_longue = ['A'] * 50000 + ['B'] * 50000
    
    try:
        result = runs_test(sequence_longue)
        print(f"   ✅ Runs observés : {result['runs_observed']}")
        print(f"   ✅ Runs attendus : {result['runs_expected']:.2f}")
        print(f"   ✅ P-value : {result['pvalue']:.6f}")
        print(f"   ✅ Pas d'overflow détecté")
    except Exception as e:
        print(f"   ❌ Erreur : {e}")
        return False
    
    # Test 2: Séquence déséquilibrée (cas d'overflow potentiel)
    print("\n2. Test avec séquence très déséquilibrée")
    sequence_desequilibree = ['A'] * 99000 + ['B'] * 1000
    
    try:
        result = runs_test(sequence_desequilibree)
        print(f"   ✅ Runs observés : {result['runs_observed']}")
        print(f"   ✅ Runs attendus : {result['runs_expected']:.2f}")
        print(f"   ✅ P-value : {result['pvalue']:.6f}")
        print(f"   ✅ Gestion du déséquilibre OK")
    except Exception as e:
        print(f"   ❌ Erreur : {e}")
        return False
    
    # Test 3: Séquence avec strings longues (cas problématique)
    print("\n3. Test avec combinaisons INDEX5 longues")
    combinaisons_index5 = [
        'SYNC_pair_4_PLAYER',
        'SYNC_pair_4_BANKER', 
        'DESYNC_impair_5_TIE',
        'SYNC_pair_6_PLAYER'
    ] * 10000  # 40,000 éléments
    
    try:
        result = runs_test(combinaisons_index5)
        print(f"   ✅ Runs observés : {result['runs_observed']}")
        print(f"   ✅ Runs attendus : {result['runs_expected']:.2f}")
        print(f"   ✅ P-value : {result['pvalue']:.6f}")
        print(f"   ✅ Gestion des strings longues OK")
    except Exception as e:
        print(f"   ❌ Erreur : {e}")
        return False
    
    # Test 4: Cas limite - séquence uniforme
    print("\n4. Test avec séquence uniforme (cas limite)")
    sequence_uniforme = ['A'] * 1000
    
    try:
        result = runs_test(sequence_uniforme)
        print(f"   ✅ Runs observés : {result['runs_observed']}")
        print(f"   ✅ Runs attendus : {result['runs_expected']:.2f}")
        print(f"   ✅ P-value : {result['pvalue']:.6f}")
        print(f"   ✅ Gestion séquence uniforme OK")
    except Exception as e:
        print(f"   ❌ Erreur : {e}")
        return False
    
    print("\n✅ TOUS LES TESTS RUNS_TEST RÉUSSIS")
    return True


def test_analyseur_avec_donnees_problematiques():
    """
    Teste l'analyseur avec des données qui causaient des erreurs de clés
    """
    print("\n🧪 TEST DE L'ANALYSEUR AVEC DONNÉES PROBLÉMATIQUES")
    print("=" * 55)
    
    # Créer des données de test avec INDEX5
    print("\n1. Génération de données de test...")
    
    combinaisons_index5 = [
        'SYNC_pair_4_PLAYER',
        'SYNC_pair_4_BANKER', 
        'SYNC_pair_4_TIE',
        'DESYNC_pair_6_PLAYER',
        'DESYNC_pair_6_BANKER',
        'DESYNC_pair_6_TIE'
    ]
    
    # Générer 2000 mains
    n_mains = 2000
    mains_test = []
    
    for i in range(n_mains):
        combo = np.random.choice(combinaisons_index5)
        parts = combo.split('_')
        index1 = parts[0]
        index2 = f"{parts[1]}_{parts[2]}"
        index3 = parts[3]
        
        main = {
            'index1_sync_state': index1,
            'index2_cards_category': index2,
            'index3_result': index3,
            'index5_combined': combo,
            'numero_main': i + 1
        }
        mains_test.append(main)
    
    # Créer le dataset
    dataset_test = {
        'metadata': {
            'version': '1.0',
            'date_creation': datetime.now().isoformat(),
            'type': 'test_corrections',
            'nb_parties': 1,
            'nb_mains_total': n_mains
        },
        'parties': [{
            'numero_partie': 1,
            'mains': mains_test
        }]
    }
    
    # Sauvegarder
    fichier_test = 'dataset_test_corrections.json'
    with open(fichier_test, 'w', encoding='utf-8') as f:
        json.dump(dataset_test, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ Dataset créé : {fichier_test}")
    
    try:
        # 2. Tester l'analyseur
        print("\n2. Test de l'analyseur corrigé...")
        analyseur = AnalyseurSequencesLupasco(fichier_test)
        
        # Charger les données
        print("   📂 Chargement des données...")
        analyseur.charger_donnees()
        
        # Vérifier INDEX5
        if 'INDEX5' not in analyseur.sequences:
            raise ValueError("INDEX5 non trouvé")
        
        print(f"   ✅ INDEX5 chargé : {len(analyseur.sequences['INDEX5'])} valeurs")
        
        # 3. Lancer l'analyse INDEX5 avec formules exactes
        print("   🔬 Analyse INDEX5 avec formules exactes...")
        resultats_index5 = analyseur.analyser_index5_avec_formules_exactes()
        
        # Vérifier les résultats
        print("\n3. Vérification des résultats...")
        
        # Métriques globales
        globale = resultats_index5['analyse_globale']
        print(f"   ✅ Entropie Shannon : {globale['entropie_shannon']:.4f} bits")
        print(f"   ✅ Coefficient Gini : {globale['coefficient_gini']:.4f}")
        print(f"   ✅ Runs test p-value : {globale['runs_test']['pvalue']:.6f}")
        
        # Vérifier qu'il n'y a pas de NaN
        if np.isnan(globale['runs_test']['pvalue']):
            print("   ❌ P-value NaN détectée")
            return False
        
        # Combinaisons analysées
        par_combo = resultats_index5['analyse_par_combinaison']
        print(f"   ✅ Combinaisons analysées : {len(par_combo)}")
        
        # Vérifier quelques combinaisons
        for combo, stats in list(par_combo.items())[:3]:
            if np.isnan(stats['runs_test']['p_value']):
                print(f"   ❌ P-value NaN pour {combo}")
                return False
            print(f"   ✅ {combo} : p-value = {stats['runs_test']['p_value']:.6f}")
        
        # 4. Test de génération de rapport (sans le sauvegarder)
        print("\n4. Test de génération de rapport...")
        
        # Simuler la génération de rapport en mémoire
        try:
            # Juste tester que les clés existent
            if 'INDEX5_FORMULES_EXACTES' in analyseur.resultats:
                print("   ✅ Résultats INDEX5 disponibles pour rapport")
            else:
                print("   ❌ Résultats INDEX5 manquants")
                return False
            
            print("   ✅ Structure de rapport validée")
            
        except Exception as e:
            print(f"   ❌ Erreur génération rapport : {e}")
            return False
        
        print("\n✅ TOUS LES TESTS ANALYSEUR RÉUSSIS")
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU TEST : {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Nettoyage
        if os.path.exists(fichier_test):
            os.remove(fichier_test)
            print(f"\n🧹 Fichier de test supprimé : {fichier_test}")


def main():
    """Fonction principale de test"""
    print("🧪 TEST DES CORRECTIONS OVERFLOW ET ERREURS DE CLÉS")
    print("📅 Date:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("🎯 Validation des corrections apportées")
    
    # Test 1: Corrections overflow runs_test
    succes_runs = test_runs_test_overflow()
    
    # Test 2: Corrections analyseur et clés manquantes
    succes_analyseur = test_analyseur_avec_donnees_problematiques()
    
    # Résumé final
    print("\n" + "="*60)
    print("📋 RÉSUMÉ DES TESTS")
    print("="*60)
    
    if succes_runs:
        print("✅ Corrections overflow runs_test : RÉUSSIES")
    else:
        print("❌ Corrections overflow runs_test : ÉCHEC")
    
    if succes_analyseur:
        print("✅ Corrections analyseur et clés : RÉUSSIES")
    else:
        print("❌ Corrections analyseur et clés : ÉCHEC")
    
    if succes_runs and succes_analyseur:
        print("\n🎉 TOUTES LES CORRECTIONS VALIDÉES !")
        print("🚀 L'analyseur est maintenant robuste et prêt pour la production")
        print("\n📋 CORRECTIONS APPLIQUÉES :")
        print("   ✅ Gestion des overflows dans le test des runs")
        print("   ✅ Protection contre les divisions par zéro")
        print("   ✅ Gestion des clés manquantes dans les rapports")
        print("   ✅ Validation des calculs numériques")
        print("   ✅ Messages d'erreur explicites")
    else:
        print("\n❌ CERTAINES CORRECTIONS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return succes_runs and succes_analyseur


if __name__ == "__main__":
    main()
