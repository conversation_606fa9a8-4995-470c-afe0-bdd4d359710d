#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test rapide du fichier JSON
"""

import json
import os

fichier = "dataset_baccarat_lupasco_20250617_232800.json"

print(f"🔍 Test du fichier : {fichier}")

# Vérifier l'existence
if os.path.exists(fichier):
    taille = os.path.getsize(fichier)
    print(f"✅ Fichier trouvé, taille : {taille:,} octets ({taille/1024/1024:.1f} MB)")
else:
    print("❌ Fichier non trouvé")
    exit(1)

# Essayer de charger
print("🔄 Chargement en cours...")
try:
    with open(fichier, 'r', encoding='utf-8') as f:
        donnees = json.load(f)
    
    print(f"✅ JSON chargé avec succès")
    print(f"📊 Nombre de parties : {len(donnees['parties'])}")
    
    # Tester la première partie
    if donnees['parties']:
        premiere_partie = donnees['parties'][0]
        print(f"📋 Première partie : {len(premiere_partie['mains'])} mains")
        
        # Tester la première main
        if premiere_partie['mains']:
            premiere_main = premiere_partie['mains'][0]
            print(f"🎯 Première main : INDEX1={premiere_main['index1_sync_state']}, INDEX2={premiere_main['index2_cards_category']}, INDEX3={premiere_main['index3_result']}")
    
except Exception as e:
    print(f"❌ Erreur : {e}")
