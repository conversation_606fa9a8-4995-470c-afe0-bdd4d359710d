# APPRENTISSAGE STATISTIQUE SUPERVISÉ - UNIVERSITÉ PARIS-SACLAY

**Source :** https://www.imo.universite-paris-saclay.fr/~christine.keribin/STA203/ENSTA-STA203-Poly-2024.pdf

## CONTENU PRINCIPAL

### 1. VALIDATION DE MODÈLES
- Validation croisée (cross-validation)
- Échantillons d'entraînement et de test
- Métriques de performance
- Estimation de l'erreur de prédiction

### 2. ÉVALUATION PRÉDICTIVE
- Taux d'erreur de prédiction
- Précision et rappel
- Courbes ROC
- Matrices de confusion

### 3. SÉLECTION DE MODÈLES
- Critères de sélection
- Compromis biais-variance
- Régularisation
- Validation des hypothèses

### 4. TECHNIQUES AVANCÉES
- Bootstrap
- Permutation tests
- Analyse de sensibilité
- Robustesse des modèles

## PERTINENCE POUR NOTRE PROJET
- Méthodes de validation des prédictions Lupasco
- Calcul rigoureux des précisions prédictives
- Tests de robustesse du système
- Évaluation des performances INDEX2 et INDEX3
